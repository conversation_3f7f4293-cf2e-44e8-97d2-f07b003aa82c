#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Menu Layout Script                                 ║
# ║                                                                          ║
# ║ This script provides the menu layout for the H-CareCloud management      ║
# ║ system. It is used by runall.sh to display the menu.                     ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Function to display the menu
display_menu() {
    # Colors for output (in case they're not inherited)
    local GREEN='\033[0;32m'
    local YELLOW='\033[1;33m'
    local RED='\033[0;31m'
    local BLUE='\033[0;34m'
    local CYAN='\033[0;36m'
    local MAGENTA='\033[0;35m'
    local WHITE='\033[1;37m'
    local BOLD='\033[1m'
    local NC='\033[0m' # No Color

    # MODERN ENTERPRISE MENU DESIGN
    echo -e "\n${BOLD}${CYAN}╭─────────────────────────────────────────────────────────────────────────╮${NC}"
    echo -e "${BOLD}${CYAN}│${NC}                     ${BOLD}${GREEN}🚀 ENTERPRISE COMMAND CENTER${NC}                     ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}╰─────────────────────────────────────────────────────────────────────────╯${NC}"

    # CORE INFRASTRUCTURE - Modern card design
    echo -e "\n${BOLD}${BLUE}┌─ 🏗️ CORE INFRASTRUCTURE ─────────────────────────────────────────────┐${NC}"
    echo -e "${BOLD}${BLUE}│${NC}                                                                     ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}  ${GREEN}▶ 1${NC}   🐳 Docker Management & Orchestration                      ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}  ${GREEN}▶ 2${NC}   🌐 Environment & Configuration Management                ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}  ${GREEN}▶ 3${NC}   🔧 System Tools & Diagnostics                            ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}                                                                     ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # HEALTHCARE MANAGEMENT SYSTEM
    echo -e "\n${BOLD}${GREEN}┌─ 🏥 H-CARECLOUD HEALTHCARE SYSTEM ──────────────────────────────────┐${NC}"
    echo -e "${BOLD}${GREEN}│${NC}                                                                     ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}  ${GREEN}▶ 11${NC}  🐘 Laravel Application Management                         ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}  ${GREEN}▶ 12${NC}  📦 Dependencies & Package Management                      ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}  ${GREEN}▶ 13${NC}  🔗 Storage & File System Management                      ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}  ${GREEN}▶ 14${NC}  🧹 Maintenance & Cleanup Operations                      ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}  ${GREEN}▶ 15${NC}  🔄 Full System Rebuild & Recovery                        ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}  ${GREEN}▶ 16${NC}  🧙 Intelligent Setup Wizard                              ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}│${NC}                                                                     ${BOLD}${GREEN}│${NC}"
    echo -e "${BOLD}${GREEN}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # FRONTEND MANAGEMENT DASHBOARD
    echo -e "\n${BOLD}${CYAN}┌─ ⚛️ H-CAREMANAGER FRONTEND DASHBOARD ───────────────────────────────┐${NC}"
    echo -e "${BOLD}${CYAN}│${NC}                                                                     ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}│${NC}  ${GREEN}▶ 17${NC}  🎨 Production Build & Deployment                         ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}│${NC}  ${GREEN}▶ 18${NC}  🛠️ Development Mode & Hot Reload                         ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}│${NC}  ${GREEN}▶ 19${NC}  📦 Node.js & NPM Package Management                      ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}│${NC}  ${GREEN}▶ 20${NC}  🧹 Frontend Cleanup & Optimization                       ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}│${NC}  ${GREEN}▶ 21${NC}  🛡️ Security Vulnerability Management                     ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}│${NC}                                                                     ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # ENTERPRISE BACKUP & RECOVERY
    echo -e "\n${BOLD}${YELLOW}┌─ 💾 ENTERPRISE BACKUP & DISASTER RECOVERY ──────────────────────────┐${NC}"
    echo -e "${BOLD}${YELLOW}│${NC}                                                                     ${BOLD}${YELLOW}│${NC}"
    echo -e "${BOLD}${YELLOW}│${NC}  ${GREEN}▶ 22${NC}  💾 Professional Backup Management System                 ${BOLD}${YELLOW}│${NC}"
    echo -e "${BOLD}${YELLOW}│${NC}  ${GREEN}▶ 23${NC}  📤 Disaster Recovery & Restore Operations                ${BOLD}${YELLOW}│${NC}"
    echo -e "${BOLD}${YELLOW}│${NC}                                                                     ${BOLD}${YELLOW}│${NC}"
    echo -e "${BOLD}${YELLOW}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # ADVANCED MANAGEMENT TOOLS
    echo -e "\n${BOLD}${MAGENTA}┌─ 🎛️ ADVANCED MANAGEMENT TOOLS ──────────────────────────────────────┐${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}                                                                     ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}  ${GREEN}▶ 24${NC}  🌐 Environment & Configuration Management                ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}  ${GREEN}▶ 25${NC}  🛠️ Development Tools & Utilities                         ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}  ${GREEN}▶ 26${NC}  🌐 Network & Connectivity Management                    ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}                                                                     ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # SYSTEM INFORMATION & SUPPORT
    echo -e "\n${BOLD}${BLUE}┌─ 📊 SYSTEM INFORMATION & SUPPORT ────────────────────────────────────┐${NC}"
    echo -e "${BOLD}${BLUE}│${NC}                                                                     ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}  ${GREEN}▶ 27${NC}  📚 Documentation & Help Center                           ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}  ${GREEN}▶ 28${NC}  ⚙️ System Configuration & Details                       ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}│${NC}                                                                     ${BOLD}${BLUE}│${NC}"
    echo -e "${BOLD}${BLUE}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # ENTERPRISE MAINTENANCE & TROUBLESHOOTING
    echo -e "\n${BOLD}${RED}┌─ ⚠️ ENTERPRISE MAINTENANCE & TROUBLESHOOTING ───────────────────────┐${NC}"
    echo -e "${BOLD}${RED}│${NC}                                                                     ${BOLD}${RED}│${NC}"
    echo -e "${BOLD}${RED}│${NC}  ${GREEN}▶ 29${NC}  📝 System File & Encoding Management                     ${BOLD}${RED}│${NC}"
    echo -e "${BOLD}${RED}│${NC}  ${GREEN}▶ 30${NC}  🐳 Docker Platform Troubleshooting                       ${BOLD}${RED}│${NC}"
    echo -e "${BOLD}${RED}│${NC}  ${GREEN}▶ 31${NC}  🔍 Code Quality & Cleanup Operations                     ${BOLD}${RED}│${NC}"
    echo -e "${BOLD}${RED}│${NC}                                                                     ${BOLD}${RED}│${NC}"
    echo -e "${BOLD}${RED}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # SYSTEM EXIT
    echo -e "\n${BOLD}${MAGENTA}┌─ 🚪 SYSTEM EXIT ─────────────────────────────────────────────────────┐${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}                                                                     ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}  ${RED}▶ 0${NC}   🚪 Exit H-CareCloud Enterprise Management System        ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}│${NC}                                                                     ${BOLD}${MAGENTA}│${NC}"
    echo -e "${BOLD}${MAGENTA}└─────────────────────────────────────────────────────────────────────────┘${NC}"

    # MODERN COMMAND PROMPT
    echo -e "\n${BOLD}${CYAN}╭─────────────────────────────────────────────────────────────────────────╮${NC}"
    echo -e "${BOLD}${CYAN}│${NC}                    ${BOLD}${YELLOW}▶ ENTER COMMAND [0-31]:${NC}                     ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}╰─────────────────────────────────────────────────────────────────────────╯${NC}"
}

# Function to display the main menu
show_menu() {
    local should_clear=${1:-false}
    
    # Force reload environment variables to ensure we get the latest value
    if [ -f "${PROJECT_ROOT}/.env" ]; then
        local env_value=$(grep "^APP_ENV=" "${PROJECT_ROOT}/.env" | cut -d= -f2 | tr -d '"' | tr -d "'" | xargs)
        CURRENT_ENV="${env_value}"
        export CURRENT_ENV
    else
        echo -e "${RED}Error: .env file not found${NC}"
        exit 1
    fi
    
    local current_env="${CURRENT_ENV}"

    # Always clear screen to prevent display issues
    clear

    # Get container status information before drawing menu
    local docker_status=$(check_docker_status && echo "Running" || echo "Not Running")
    local containers_up=$(docker-compose ps 2>/dev/null | grep -c "Up" || echo "0")
    local total_containers=$(docker-compose ps -q 2>/dev/null | wc -l || echo "0")

    # Detect Docker type
    local docker_type="Docker Engine"
    if docker info 2>/dev/null | grep -q "Docker Desktop"; then
        docker_type="Docker Desktop"
    fi
    local docker_version=$(docker --version 2>/dev/null | awk '{print $3}' | tr -d ',' || echo "Unknown")

    # Get system information
    local os_info=$(uname -s)
    local kernel_version=$(uname -r)
    local cpu_info=$(grep -m 1 "model name" /proc/cpuinfo 2>/dev/null | cut -d ':' -f 2 | sed 's/^[ \t]*//' || echo "Unknown CPU")
    local memory_info=$(free -h | grep "Mem:" | awk '{print $2}')

    # Windows-specific information
    local is_windows=false
    local win_version=""
    local win_cpu=""
    if [[ "$os_info" == *"MINGW"* ]] || [[ "$os_info" == *"MSYS"* ]] || [[ "$os_info" == *"CYGWIN"* ]]; then
        is_windows=true
        win_version=$(cmd.exe /c "ver" 2>/dev/null | grep -o "Version [0-9\.]*" || echo "Unknown Windows")
        win_cpu=$(wmic cpu get name 2>/dev/null | grep -v "Name" | head -n 1 || echo "Unknown CPU")
    fi

    # Display header with system information
    display_header "$current_env" "$docker_status" "$containers_up" "$total_containers" "$docker_type" "$docker_version" "$os_info" "$kernel_version" "$cpu_info" "$memory_info" "$is_windows" "$win_version" "$win_cpu"

    # Display the menu
    display_menu
}

# Function to list all menu options (used by API)
list_menu_options() {
    echo "1: Docker Management & Orchestration"
    echo "2: Environment & Configuration Management"
    echo "3: System Tools & Diagnostics"
    echo "11: Laravel Application Management"
    echo "12: Dependencies & Package Management"
    echo "13: Storage & File System Management"
    echo "14: Maintenance & Cleanup Operations"
    echo "15: Full System Rebuild & Recovery"
    echo "16: Intelligent Setup Wizard"
    echo "17: Production Build & Deployment"
    echo "18: Development Mode & Hot Reload"
    echo "19: Node.js & NPM Package Management"
    echo "20: Frontend Cleanup & Optimization"
    echo "21: Security Vulnerability Management"
    echo "22: Professional Backup Management System"
    echo "23: Disaster Recovery & Restore Operations"
    echo "24: Environment & Configuration Management"
    echo "25: Development Tools & Utilities"
    echo "26: Network & Connectivity Management"
    echo "27: Documentation & Help Center"
    echo "28: System Configuration & Details"
    echo "29: System File & Encoding Management"
    echo "30: Docker Platform Troubleshooting"
    echo "31: Code Quality & Cleanup Operations"
    echo "0: Exit H-CareCloud Enterprise Management System"
}
