#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Menu Layout Script                                 ║
# ║                                                                          ║
# ║ This script provides the menu layout for the H-CareCloud management      ║
# ║ system. It is used by runall.sh to display the menu.                     ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Function to display the menu
display_menu() {
    # Colors for output (in case they're not inherited)
    local GREEN='\033[0;32m'
    local YELLOW='\033[1;33m'
    local RED='\033[0;31m'
    local BLUE='\033[0;34m'
    local CYAN='\033[0;36m'
    local MAGENTA='\033[0;35m'
    local BOLD='\033[1m'
    local NC='\033[0m' # No Color

    # ULTRA-MODERN SLEEK MENU DESIGN
    echo -e "\n${BOLD}${CYAN}▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓${NC}"
    echo -e "${BOLD}${CYAN}█${NC}                     ${BOLD}${GREEN}🚀 ENTERPRISE COMMAND CENTER${NC}                     ${BOLD}${CYAN}█${NC}"
    echo -e "${BOLD}${CYAN}▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓${NC}"

    # CORE INFRASTRUCTURE - Modern card design
    echo -e "\n${BOLD}${MAGENTA}▌${NC}${BOLD}${WHITE} 🏗️ CORE INFRASTRUCTURE ${NC}${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}                                                                     ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}  ${GREEN}▶ 1${NC}   🐳 Docker Management & Orchestration                      ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}  ${GREEN}▶ 2${NC}   🌐 Environment & Configuration Management                ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}  ${GREEN}▶ 3${NC}   🔧 System Tools & Diagnostics                            ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}                                                                     ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # HEALTHCARE MANAGEMENT SYSTEM
    echo -e "\n${BOLD}${BLUE}▌${NC}${BOLD}${WHITE} 🏥 H-CARECLOUD HEALTHCARE SYSTEM ${NC}${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}                                                                     ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}  ${GREEN}▶ 11${NC}  🐘 Laravel Application Management                         ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}  ${GREEN}▶ 12${NC}  📦 Dependencies & Package Management                      ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}  ${GREEN}▶ 13${NC}  🔗 Storage & File System Management                      ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}  ${GREEN}▶ 14${NC}  🧹 Maintenance & Cleanup Operations                      ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}  ${GREEN}▶ 15${NC}  🔄 Full System Rebuild & Recovery                        ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}  ${GREEN}▶ 16${NC}  🧙 Intelligent Setup Wizard                              ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌${NC}                                                                     ${BOLD}${BLUE}▐${NC}"
    echo -e "${BOLD}${BLUE}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # FRONTEND MANAGEMENT DASHBOARD
    echo -e "\n${BOLD}${CYAN}▌${NC}${BOLD}${WHITE} ⚛️ H-CAREMANAGER FRONTEND DASHBOARD ${NC}${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}                                                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 17${NC}  🎨 Production Build & Deployment                         ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 18${NC}  🛠️ Development Mode & Hot Reload                         ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 19${NC}  📦 Node.js & NPM Package Management                      ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 20${NC}  🧹 Frontend Cleanup & Optimization                       ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 21${NC}  🛡️ Security Vulnerability Management                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}                                                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # ENTERPRISE BACKUP & RECOVERY
    echo -e "\n${BOLD}${YELLOW}▌${NC}${BOLD}${WHITE} 💾 ENTERPRISE BACKUP & DISASTER RECOVERY ${NC}${BOLD}${YELLOW}▐${NC}"
    echo -e "${BOLD}${YELLOW}▌${NC}                                                                     ${BOLD}${YELLOW}▐${NC}"
    echo -e "${BOLD}${YELLOW}▌${NC}  ${GREEN}▶ 22${NC}  💾 Professional Backup Management System                 ${BOLD}${YELLOW}▐${NC}"
    echo -e "${BOLD}${YELLOW}▌${NC}  ${GREEN}▶ 23${NC}  📤 Disaster Recovery & Restore Operations                ${BOLD}${YELLOW}▐${NC}"
    echo -e "${BOLD}${YELLOW}▌${NC}                                                                     ${BOLD}${YELLOW}▐${NC}"
    echo -e "${BOLD}${YELLOW}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # ADVANCED MANAGEMENT TOOLS
    echo -e "\n${BOLD}${GREEN}▌${NC}${BOLD}${WHITE} 🎛️ ADVANCED MANAGEMENT TOOLS ${NC}${BOLD}${GREEN}▐${NC}"
    echo -e "${BOLD}${GREEN}▌${NC}                                                                     ${BOLD}${GREEN}▐${NC}"
    echo -e "${BOLD}${GREEN}▌${NC}  ${GREEN}▶ 24${NC}  🌐 Environment & Configuration Management                ${BOLD}${GREEN}▐${NC}"
    echo -e "${BOLD}${GREEN}▌${NC}  ${GREEN}▶ 25${NC}  🛠️ Development Tools & Utilities                         ${BOLD}${GREEN}▐${NC}"
    echo -e "${BOLD}${GREEN}▌${NC}  ${GREEN}▶ 26${NC}  🌐 Network & Connectivity Management                    ${BOLD}${GREEN}▐${NC}"
    echo -e "${BOLD}${GREEN}▌${NC}                                                                     ${BOLD}${GREEN}▐${NC}"
    echo -e "${BOLD}${GREEN}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # SYSTEM INFORMATION & SUPPORT
    echo -e "\n${BOLD}${CYAN}▌${NC}${BOLD}${WHITE} 📊 SYSTEM INFORMATION & SUPPORT ${NC}${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}                                                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 27${NC}  📚 Documentation & Help Center                           ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 28${NC}  ⚙️ System Configuration & Details                       ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}                                                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # ENTERPRISE MAINTENANCE & TROUBLESHOOTING
    echo -e "\n${BOLD}${RED}▌${NC}${BOLD}${WHITE} ⚠️ ENTERPRISE MAINTENANCE & TROUBLESHOOTING ${NC}${BOLD}${RED}▐${NC}"
    echo -e "${BOLD}${RED}▌${NC}                                                                     ${BOLD}${RED}▐${NC}"
    echo -e "${BOLD}${RED}▌${NC}  ${GREEN}▶ 29${NC}  📝 System File & Encoding Management                     ${BOLD}${RED}▐${NC}"
    echo -e "${BOLD}${RED}▌${NC}  ${GREEN}▶ 30${NC}  🐳 Docker Platform Troubleshooting                       ${BOLD}${RED}▐${NC}"
    echo -e "${BOLD}${RED}▌${NC}  ${GREEN}▶ 31${NC}  🔍 Code Quality & Cleanup Operations                     ${BOLD}${RED}▐${NC}"
    echo -e "${BOLD}${RED}▌${NC}                                                                     ${BOLD}${RED}▐${NC}"
    echo -e "${BOLD}${RED}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # SYSTEM EXIT
    echo -e "\n${BOLD}${MAGENTA}▌${NC}${BOLD}${WHITE} 🚪 SYSTEM EXIT ${NC}${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}                                                                     ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}  ${RED}▶ 0${NC}   🚪 Exit H-CareCloud Enterprise Management System        ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌${NC}                                                                     ${BOLD}${MAGENTA}▐${NC}"
    echo -e "${BOLD}${MAGENTA}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"

    # ULTRA-MODERN COMMAND PROMPT
    echo -e "\n${BOLD}${CYAN}▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓${NC}"
    echo -e "${BOLD}${CYAN}█${NC}                    ${BOLD}${YELLOW}▶ ENTER COMMAND [0-36]:${NC}                     ${BOLD}${CYAN}█${NC}"
    echo -e "${BOLD}${CYAN}▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓${NC}"
}

# Function to display the main menu
show_menu() {
    local should_clear=${1:-false}
    
    # Force reload environment variables to ensure we get the latest value
    if [ -f "${PROJECT_ROOT}/.env" ]; then
        local env_value=$(grep "^APP_ENV=" "${PROJECT_ROOT}/.env" | cut -d= -f2 | tr -d '"' | tr -d "'" | xargs)
        CURRENT_ENV="${env_value}"
        export CURRENT_ENV
    else
        echo -e "${RED}Error: .env file not found${NC}"
        exit 1
    fi
    
    local current_env="${CURRENT_ENV}"

    # Always clear screen to prevent display issues
    clear

    # Get container status information before drawing menu
    local docker_status=$(check_docker_status && echo "Running" || echo "Not Running")
    local containers_up=$(docker-compose ps 2>/dev/null | grep -c "Up" || echo "0")
    local total_containers=$(docker-compose ps -q 2>/dev/null | wc -l || echo "0")

    # Detect Docker type
    local docker_type="Docker Engine"
    if docker info 2>/dev/null | grep -q "Docker Desktop"; then
        docker_type="Docker Desktop"
    fi
    local docker_version=$(docker --version 2>/dev/null | awk '{print $3}' | tr -d ',' || echo "Unknown")

    # Get system information
    local os_info=$(uname -s)
    local kernel_version=$(uname -r)
    local cpu_info=$(grep -m 1 "model name" /proc/cpuinfo 2>/dev/null | cut -d ':' -f 2 | sed 's/^[ \t]*//' || echo "Unknown CPU")
    local memory_info=$(free -h | grep "Mem:" | awk '{print $2}')

    # Windows-specific information
    local is_windows=false
    local win_version=""
    local win_cpu=""
    if [[ "$os_info" == *"MINGW"* ]] || [[ "$os_info" == *"MSYS"* ]] || [[ "$os_info" == *"CYGWIN"* ]]; then
        is_windows=true
        win_version=$(cmd.exe /c "ver" 2>/dev/null | grep -o "Version [0-9\.]*" || echo "Unknown Windows")
        win_cpu=$(wmic cpu get name 2>/dev/null | grep -v "Name" | head -n 1 || echo "Unknown CPU")
    fi

    # Display header with system information
    display_header "$current_env" "$docker_status" "$containers_up" "$total_containers" "$docker_type" "$docker_version" "$os_info" "$kernel_version" "$cpu_info" "$memory_info" "$is_windows" "$win_version" "$win_cpu"

    # Display the menu
    display_menu
}

# Function to list all menu options (used by API)
list_menu_options() {
    echo "1: Docker Management Menu"
    echo "11: PHP/Artisan Commands"
    echo "12: Rebuild App Dependencies"
    echo "13: Fix Storage Link"
    echo "14: Clean Vendor Folder"
    echo "15: Rebuild Everything (Full)"
    echo "16: Setup Wizard"
    echo "17: Build Manager UI (Production)"
    echo "18: Dev Mode Manager UI"
    echo "19: Node/NPM Commands"
    echo "20: Clean Node.js Files"
    echo "21: Fix NPM Vulnerabilities"
    echo "22: Professional Backup System"
    echo "23: Restore Backup"
    echo "24: Environment Management Menu"
    echo "25: Development Tools Menu"
    echo "26: Network Management Menu"
    echo "27: Help & Documentation"
    echo "28: Show Config Details"
    echo "29: Fix Line Ending Issues"
    echo "30: Fix Docker Desktop Issues"
    echo "31: Code Cleanup Utility"
    echo "0: Exit"
}
