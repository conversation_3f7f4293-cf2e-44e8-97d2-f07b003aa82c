#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Main Loop Function                                 ║
# ║                                                                          ║
# ║ This script provides the main loop function for the H-CareCloud          ║
# ║ management system. It is used by runall.sh to handle menu options.       ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Colors for output (in case they're not inherited)
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Ensure SCRIPT_DIR is defined
if [ -z "${SCRIPT_DIR}" ]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
fi

# PROJECT_ROOT should already be defined by ru-helper.sh
# This is just a fallback in case ru-helper.sh wasn't sourced
if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "\033[1;33mWarning: PROJECT_ROOT not defined. ru-helper.sh may not have been sourced.\033[0m"
    # Set project root to the parent directory of docker
    PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && cd .. && pwd)"
    echo -e "\033[0;36mUsing fallback PROJECT_ROOT: ${PROJECT_ROOT}\033[0m"
fi

# Main loop function
main_loop() {
    local choice
    local refresh_menu=true

    while true; do
        if [ "$refresh_menu" = true ]; then
            if [ "$CLEAR_SCREEN" = "true" ]; then
                clear
            fi

            # Display menu
            show_menu "$CLEAR_SCREEN"

            refresh_menu=false
        fi

        # Get user selection
        echo -ne "${CYAN}Enter your choice${NC} [0-36]: "
        read -r choice

        # Exit if choice is 0
        if [ "$choice" = "0" ]; then
            echo -e "${GREEN}Exiting H-CareCloud Management System. Goodbye!${NC}"
            break
        fi

        # Process user choice
        case $choice in
            # Docker Management (1)
            1)
                handle_docker_management $choice
                refresh_menu=true
                ;;

            # H-CareCloud Laravel (11-16)
            11|12|13|14|15|16)
                handle_laravel_management $choice
                refresh_menu=true
                ;;

            # H-CareManager React (17-21)
            17|18|19|20|21)
                handle_react_management $choice
                refresh_menu=true
                ;;

            # Backup & Recovery (22-23)
            22|23)
                refresh_menu=true
                ;;

            # Environment Management (24)
            24)
                handle_environment_management $choice
                refresh_menu=true
                ;;

            # Development Tools (25)
            25)
                handle_development_tools $choice
                refresh_menu=true
                ;;

            # Network & Connectivity (26)
            26)
                handle_network_management $choice
                refresh_menu=true
                ;;

            # System & Help (27-28)
            27|28)
                handle_system_help $choice
                refresh_menu=true
                ;;

            # Advanced Operations (29-36)
            29|30|31|32|33|34|35|36)
                handle_special_operations $choice
                refresh_menu=true
                ;;

            *)
                echo -e "${RED}Invalid option. Please try again.${NC}"
                sleep 1
                refresh_menu=true
                ;;
        esac
    done
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-main.sh"
    return 1
fi

