#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Development Tool Functions                        ║
# ║                                                                          ║
# ║ This script provides development tool functions for H-CareCloud.        ║
# ║ It handles PHP, Node.js, database, and other development operations.    ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Ensure SCRIPT_DIR is defined
if [ -z "${SCRIPT_DIR}" ]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
fi

# Source helper functions first - NO FALLBACKS
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
    echo -e "${CYAN}PROJECT_ROOT: ${PROJECT_ROOT}${NC}"
else
    echo -e "${RED}FATAL ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}" >&2
    return 1
fi

# Verify PROJECT_ROOT is set
if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "${RED}FATAL ERROR: PROJECT_ROOT not set. ru-helper.sh must be sourced properly.${NC}" >&2
    return 1
fi

# Source common functions if not already sourced - PRODUCTION READY
if ! type execute_with_progress &>/dev/null; then
    if [ -f "${SCRIPT_DIR}/ru-functions.sh" ]; then
        source "${SCRIPT_DIR}/ru-functions.sh"
    else
        echo -e "${RED}FATAL ERROR: ru-functions.sh not found at ${SCRIPT_DIR}/ru-functions.sh${NC}" >&2
        return 1
    fi
fi

# Function to check if a container is running
check_container_running() {
    local container_name=$1

    # Use container_running from ru-functions.sh if available
    if type container_running &>/dev/null; then
        if container_running "$container_name"; then
            return 0  # Container is running
        elif [[ "$container_name" != hcarecloud-* ]] && container_running "hcarecloud-$container_name"; then
            return 0  # Container is running with hcarecloud- prefix
        else
            return 1  # Container is not running
        fi
    else
        # Fallback if container_running is not available
        # Check if container is running using both docker-compose and Docker CLI
        if docker-compose ps 2>/dev/null | grep -q "$container_name.*Up"; then
            return 0  # Container is running via docker-compose
        elif docker ps 2>/dev/null | grep -q "$container_name.*Up"; then
            return 0  # Container is running via Docker CLI
        elif [[ "$container_name" != hcarecloud-* ]] && (docker-compose ps 2>/dev/null | grep -q "hcarecloud-$container_name.*Up" || docker ps 2>/dev/null | grep -q "hcarecloud-$container_name.*Up"); then
            return 0  # Container is running with hcarecloud- prefix
        else
            return 1  # Container is not running
        fi
    fi
}

# Function to ensure container is running (using the common function from ru-functions.sh)
ensure_container_running() {
    # Just call the common function
    if ! command -v ensure_container_running &>/dev/null; then
        # If the common function doesn't exist, use the local implementation
        local container_name=$1
        if ! check_container_running "$container_name"; then
            echo -e "${YELLOW}Starting $container_name container...${NC}"
            docker-compose up -d $container_name

            # Wait for container to be ready
            echo -ne "${YELLOW}Waiting for container to be ready...${NC}"
            for i in {1..30}; do
                if check_container_running "$container_name"; then
                    echo -e "${GREEN}✓${NC}"
                    return 0
                fi
                echo -n "."
                sleep 1
            done
            echo -e "\n${RED}Error: Container failed to start${NC}"
            return 1
        fi
        return 0
    else
        # Call the common function from ru-functions.sh
        ensure_container_running "$1"
        return $?
    fi
}

# Function to run PHP/Artisan commands
run_artisan_command() {
    echo -e "${BOLD}${BLUE}⚡ PHP Artisan Command${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running
    if type is_docker_running &>/dev/null; then
        if ! is_docker_running; then
            echo -e "${RED}Error: Docker is not running. Cannot run Artisan commands.${NC}"
            echo -e "${YELLOW}Please start Docker and try again.${NC}"
            return 1
        fi
    elif ! ensure_docker_running; then
        echo -e "${RED}Cannot run Artisan commands because Docker is not running.${NC}"
        return 1
    fi

    echo -e "${YELLOW}Available commands:${NC}"
    echo -e "  ${GREEN}1.${NC} Run migrations"
    echo -e "  ${GREEN}2.${NC} Run seeders"
    echo -e "  ${GREEN}3.${NC} Clear cache"
    echo -e "  ${GREEN}4.${NC} Generate key"
    echo -e "  ${GREEN}5.${NC} Custom command"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-5]: "
    read -r choice

    case $choice in
        1)
            # Enhanced migration options
            echo -e "\n${YELLOW}Migration Options:${NC}"
            echo -e "  ${GREEN}1.${NC} Standard migration (preserve existing data)"
            echo -e "  ${GREEN}2.${NC} Fresh migration (drop all tables and recreate)"
            echo -e "  ${GREEN}0.${NC} Cancel"

            echo -ne "${CYAN}Enter migration type${NC} [0-2]: "
            read -r migration_type

            case $migration_type in
                1)
                    execute_with_progress "Running migrations (preserving data)" "docker-compose exec app php artisan migrate --force"
                    ;;
                2)
                    echo -e "${RED}WARNING: This will delete all data in your database!${NC}"
                    echo -ne "${YELLOW}Are you sure? (y/n):${NC} "
                    read -r confirm
                    if [[ "$confirm" == [Yy]* ]]; then
                        execute_with_progress "Running fresh migrations" "docker-compose exec app php artisan migrate:fresh --force"
                    fi
                    ;;
                0)
                    return 0
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        2)
            # Enhanced seeder options
            echo -e "\n${YELLOW}Seeder Options:${NC}"
            echo -e "  ${GREEN}1.${NC} Run all seeders"
            echo -e "  ${GREEN}2.${NC} Run specific seeder"
            echo -e "  ${GREEN}3.${NC} Run seeders with duplicate checking"
            echo -e "  ${GREEN}0.${NC} Cancel"

            echo -ne "${CYAN}Enter seeder option${NC} [0-3]: "
            read -r seeder_type

            case $seeder_type in
                1)
                    execute_with_progress "Running all seeders" "docker-compose exec app php artisan db:seed --force"
                    ;;
                2)
                    echo -ne "${CYAN}Enter seeder class name:${NC} "
                    read -r seeder_class
                    if [ -n "$seeder_class" ]; then
                        execute_with_progress "Running seeder $seeder_class" "docker-compose exec app php artisan db:seed --class=$seeder_class --force"
                    fi
                    ;;
                3)
                    # Create a temporary PHP script to run seeders with checks for existing records
                    temp_script="""
                    <?php
                    // Temporary script to run seeders with duplicate checking
                    require __DIR__.'/vendor/autoload.php';
                    \$app = require_once __DIR__.'/bootstrap/app.php';
                    \$kernel = \$app->make(Illuminate\\Contracts\\Console\\Kernel::class);

                    // Get all seeder classes
                    \$seederDir = __DIR__.'/database/seeders';
                    \$files = scandir(\$seederDir);
                    \$seeders = [];

                    foreach (\$files as \$file) {
                        if (pathinfo(\$file, PATHINFO_EXTENSION) === 'php') {
                            \$className = pathinfo(\$file, PATHINFO_FILENAME);
                            \$seeders[] = \$className;
                        }
                    }

                    echo \"Running seeders with duplicate checking:\\n\";
                    foreach (\$seeders as \$seeder) {
                        echo \"- Running {\$seeder}...\\n\";
                        \$kernel->call('db:seed', ['--class' => \$seeder, '--force' => true]);
                    }

                    echo \"\\nAll seeders completed.\\n\";
                    """

                    # Write the temporary script
                    docker-compose exec app bash -c "cat > /var/www/html/run_seeders_with_checks.php << 'EOL'
$temp_script
EOL"

                    # Run the temporary script
                    execute_with_progress "Running seeders with duplicate checking" "docker-compose exec app php /var/www/html/run_seeders_with_checks.php"

                    # Clean up
                    docker-compose exec app rm /var/www/html/run_seeders_with_checks.php
                    ;;
                0)
                    return 0
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        3)
            execute_with_progress "Clearing cache" "docker-compose exec app php artisan cache:clear"
            execute_with_progress "Clearing config" "docker-compose exec app php artisan config:clear"
            execute_with_progress "Clearing views" "docker-compose exec app php artisan view:clear"
            execute_with_progress "Clearing routes" "docker-compose exec app php artisan route:clear"
            ;;
        4)
            execute_with_progress "Generating key" "docker-compose exec app php artisan key:generate"
            ;;
        5)
            echo -ne "${CYAN}Enter artisan command:${NC} "
            read -r cmd
            if [ -n "$cmd" ]; then
                execute_with_progress "Running command" "docker-compose exec app php artisan $cmd"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to manage database
manage_database() {
    echo -e "${BOLD}${BLUE}🗄️ Database Management${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running
    if type is_docker_running &>/dev/null; then
        if ! is_docker_running; then
            echo -e "${RED}Error: Docker is not running. Cannot manage database.${NC}"
            echo -e "${YELLOW}Please start Docker and try again.${NC}"
            return 1
        fi
    elif ! ensure_docker_running; then
        echo -e "${RED}Cannot manage database because Docker is not running.${NC}"
        return 1
    fi

    echo -e "${YELLOW}Available operations:${NC}"
    echo -e "  ${GREEN}1.${NC} View databases"
    echo -e "  ${GREEN}2.${NC} Create database"
    echo -e "  ${GREEN}3.${NC} Drop database"
    echo -e "  ${GREEN}4.${NC} Import SQL file"
    echo -e "  ${GREEN}5.${NC} Export database"
    echo -e "  ${GREEN}6.${NC} Import demo database"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-6]: "
    read -r choice

    # Validate input is a number
    if ! [[ "$choice" =~ ^[0-6]$ ]]; then
        echo -e "${RED}Invalid option. Please enter a number between 0 and 6.${NC}"
        return 1
    fi

    case $choice in
        1)
            if ! check_container_status "db"; then
                echo -e "${RED}Database container is not running.${NC}"
                echo -e "${YELLOW}Start the database container first with:${NC} docker-compose up -d db"
                return 1
            fi
            docker-compose exec db mysql -u root -p"${DB_PASSWORD}" -e "SHOW DATABASES;"
            ;;
        2)
            echo -ne "${CYAN}Enter database name:${NC} "
            read -r dbname
            if [ -n "$dbname" ]; then
                docker-compose exec db mysql -u root -p"${DB_PASSWORD}" -e "CREATE DATABASE IF NOT EXISTS \`$dbname\`;"
            fi
            ;;
        3)
            echo -ne "${CYAN}Enter database name:${NC} "
            read -r dbname
            if [ -n "$dbname" ]; then
                echo -e "${RED}WARNING: This will permanently delete the database '$dbname'${NC}"
                echo -ne "${YELLOW}Are you sure? (y/n):${NC} "
                read -r confirm
                if [[ "$confirm" == [Yy]* ]]; then
                    docker-compose exec db mysql -u root -p"${DB_PASSWORD}" -e "DROP DATABASE IF EXISTS \`$dbname\`;"
                fi
            fi
            ;;
        4)
            echo -ne "${CYAN}Enter SQL file path:${NC} "
            read -r sqlfile
            if [ -f "$sqlfile" ]; then
                echo -ne "${CYAN}Enter target database:${NC} "
                read -r dbname
                if [ -n "$dbname" ]; then
                    echo -e "\n${YELLOW}Import Options:${NC}"
                    echo -e "  ${GREEN}1.${NC} Standard import (replace existing data)"
                    echo -e "  ${GREEN}2.${NC} Safe import (preserve existing data)"
                    echo -e "  ${GREEN}0.${NC} Cancel"

                    echo -ne "${CYAN}Enter import type${NC} [0-2]: "
                    read -r import_type

                    case $import_type in
                        1)
                            docker-compose exec -T db mysql -u root -p"${DB_PASSWORD}" "$dbname" < "$sqlfile"
                            echo -e "${GREEN}Database imported successfully.${NC}"
                            ;;
                        2)
                            # Process the SQL file to add IF NOT EXISTS to CREATE TABLE statements
                            # and add IGNORE to INSERT statements
                            temp_sql_file="/tmp/temp_import_$(date +%s).sql"

                            echo -e "${YELLOW}Processing SQL file to preserve existing data...${NC}"
                            cat "$sqlfile" | sed 's/CREATE TABLE/CREATE TABLE IF NOT EXISTS/g' | sed 's/INSERT INTO/INSERT IGNORE INTO/g' > "$temp_sql_file"

                            echo -e "${YELLOW}Importing with preservation of existing data...${NC}"
                            docker-compose exec -T db mysql -u root -p"${DB_PASSWORD}" "$dbname" < "$temp_sql_file"

                            # Clean up
                            rm "$temp_sql_file"

                            echo -e "${GREEN}Database imported successfully (preserving existing data).${NC}"
                            ;;
                        0)
                            return 0
                            ;;
                        *)
                            echo -e "${RED}Invalid option.${NC}"
                            ;;
                    esac
                fi
            else
                echo -e "${RED}File not found: $sqlfile${NC}"
            fi
            ;;
        5)
            echo -ne "${CYAN}Enter database name:${NC} "
            read -r dbname
            if [ -n "$dbname" ]; then
                echo -ne "${CYAN}Enter output file path:${NC} "
                read -r outfile
                if [ -n "$outfile" ]; then
                    docker-compose exec db mysqldump -u root -p"${DB_PASSWORD}" "$dbname" > "$outfile"
                fi
            fi
            ;;
        6)
            # Import demo database
            echo -e "${YELLOW}Scanning for demo databases...${NC}"

            # Check if database directory exists
            db_dir="${PROJECT_ROOT}/database"
            if [ ! -d "$db_dir" ]; then
                echo -e "${RED}Database directory not found: $db_dir${NC}"
                return 1
            fi

            # Find all SQL files in the database directory
            demo_files=($(find "$db_dir" -name "db-hcarecloud*.sql" -type f))

            if [ ${#demo_files[@]} -eq 0 ]; then
                echo -e "${RED}No demo database files found in $db_dir${NC}"
                return 1
            fi

            echo -e "${YELLOW}Available demo databases:${NC}"
            for i in "${!demo_files[@]}"; do
                file_name=$(basename "${demo_files[$i]}")
                file_size=$(du -h "${demo_files[$i]}" | cut -f1)
                echo -e "  ${GREEN}$((i+1)).${NC} $file_name ($file_size)"
            done
            echo -e "  ${GREEN}0.${NC} Cancel"

            echo -ne "${CYAN}Enter your choice${NC} [0-${#demo_files[@]}]: "
            read -r demo_choice

            # Validate input
            if ! [[ "$demo_choice" =~ ^[0-9]+$ ]] || [ "$demo_choice" -lt 0 ] || [ "$demo_choice" -gt "${#demo_files[@]}" ]; then
                echo -e "${RED}Invalid option.${NC}"
                return 1
            fi

            if [ "$demo_choice" -eq 0 ]; then
                return 0
            fi

            selected_file="${demo_files[$((demo_choice-1))]}"

            echo -e "${YELLOW}Selected: $(basename "$selected_file")${NC}"

            echo -e "\n${YELLOW}Import Options:${NC}"
            echo -e "  ${GREEN}1.${NC} Standard import (replace existing data)"
            echo -e "  ${GREEN}2.${NC} Safe import (preserve existing data)"
            echo -e "  ${GREEN}0.${NC} Cancel"

            echo -ne "${CYAN}Enter import type${NC} [0-2]: "
            read -r import_type

            case $import_type in
                1)
                    echo -e "${YELLOW}Importing demo database...${NC}"
                    docker-compose exec -T db mysql -u root -p"${DB_PASSWORD}" "${DB_DATABASE}" < "$selected_file"
                    echo -e "${GREEN}Demo database imported successfully.${NC}"
                    ;;
                2)
                    # Process the SQL file to add IF NOT EXISTS to CREATE TABLE statements
                    # and add IGNORE to INSERT statements
                    temp_sql_file="/tmp/temp_import_$(date +%s).sql"

                    echo -e "${YELLOW}Processing SQL file to preserve existing data...${NC}"
                    cat "$selected_file" | sed 's/CREATE TABLE/CREATE TABLE IF NOT EXISTS/g' | sed 's/INSERT INTO/INSERT IGNORE INTO/g' > "$temp_sql_file"

                    echo -e "${YELLOW}Importing with preservation of existing data...${NC}"
                    docker-compose exec -T db mysql -u root -p"${DB_PASSWORD}" "${DB_DATABASE}" < "$temp_sql_file"

                    # Clean up
                    rm "$temp_sql_file"

                    echo -e "${GREEN}Demo database imported successfully (preserving existing data).${NC}"
                    ;;
                0)
                    return 0
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to fix storage link
fix_storage_link() {
    echo -e "${BOLD}${BLUE}🔗 Fix Storage Link${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running when using Docker commands
    if type is_docker_running &>/dev/null; then
        if is_docker_running; then
            DOCKER_RUNNING=true
        else
            DOCKER_RUNNING=false
            echo -e "${YELLOW}Warning: Docker is not running. Some operations may not be available.${NC}"
        fi
    elif command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        DOCKER_RUNNING=true
    else
        DOCKER_RUNNING=false
        echo -e "${YELLOW}Warning: Docker is not running. Some operations may not be available.${NC}"
    fi

    # Check current status
    if [ -L "${PROJECT_ROOT}/public/storage" ]; then
        echo -e "${YELLOW}Storage link exists. Current status:${NC}"
        ls -la "${PROJECT_ROOT}/public/storage"

        echo -e "\n${YELLOW}Options:${NC}"
        echo -e "  ${GREEN}1.${NC} Remove and recreate storage link"
        echo -e "  ${GREEN}2.${NC} Check if link is working correctly"
        echo -e "  ${GREEN}0.${NC} Cancel"

        echo -ne "\n${CYAN}Enter your choice${NC} [0-2]: "
        read -r storage_choice

        case $storage_choice in
            0)
                echo -e "${YELLOW}Operation cancelled.${NC}"
                return 0
                ;;
            1)
                echo -e "${YELLOW}This will remove the existing storage link and create a new one.${NC}"
                echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                read -r confirm_fix

                if [[ "$confirm_fix" == [Yy]* ]]; then
                    # Remove existing link
                    rm "${PROJECT_ROOT}/public/storage"

                    # Create new link
                    if [ -f "${SCRIPT_DIR}/ru-fix-storage-link.sh" ]; then
                        echo -e "${YELLOW}Using dedicated storage link fix script...${NC}"
                        "${SCRIPT_DIR}/ru-fix-storage-link.sh"
                    else
                        if [ "$DOCKER_RUNNING" = true ]; then
                            execute_with_progress "Creating storage link" "docker-compose exec app php artisan storage:link"
                        else
                            echo -e "${RED}Cannot create storage link because Docker is not running.${NC}"
                            echo -e "${YELLOW}Please start Docker and try again.${NC}"
                            return 1
                        fi
                    fi

                    echo -e "${GREEN}✅ Storage link recreated successfully.${NC}"
                else
                    echo -e "${YELLOW}Operation cancelled.${NC}"
                fi
                ;;
            2)
                # Check if link is working by verifying target exists
                link_target=$(readlink "${PROJECT_ROOT}/public/storage")

                if [ -d "$link_target" ]; then
                    echo -e "${GREEN}✅ Storage link is working correctly.${NC}"
                    echo -e "Link target: ${CYAN}$link_target${NC}"
                else
                    echo -e "${RED}✗ Storage link exists but points to a non-existent location.${NC}"
                    echo -e "Link target: ${CYAN}$link_target${NC}"

                    echo -e "${YELLOW}Would you like to fix the link? (y/n):${NC} "
                    read -r fix_link

                    if [[ "$fix_link" == [Yy]* ]]; then
                        rm "${PROJECT_ROOT}/public/storage"

                        if [ -f "${SCRIPT_DIR}/ru-fix-storage-link.sh" ]; then
                            echo -e "${YELLOW}Using dedicated storage link fix script...${NC}"
                            "${SCRIPT_DIR}/ru-fix-storage-link.sh"
                        else
                            if [ "$DOCKER_RUNNING" = true ]; then
                                execute_with_progress "Creating storage link" "docker-compose exec app php artisan storage:link"
                            else
                                echo -e "${RED}Cannot create storage link because Docker is not running.${NC}"
                                echo -e "${YELLOW}Please start Docker and try again.${NC}"
                                return 1
                            fi
                        fi

                        echo -e "${GREEN}✅ Storage link fixed.${NC}"
                    fi
                fi
                ;;
            *)
                echo -e "${RED}Invalid option.${NC}"
                ;;
        esac
    else
        echo -e "${YELLOW}No storage link found.${NC}"
        echo -ne "${YELLOW}Would you like to create the storage link? (y/n):${NC} "
        read -r create_link

        if [[ "$create_link" == [Yy]* ]]; then
            if [ -f "${SCRIPT_DIR}/ru-fix-storage-link.sh" ]; then
                echo -e "${YELLOW}Using dedicated storage link fix script...${NC}"
                "${SCRIPT_DIR}/ru-fix-storage-link.sh"
            else
                execute_with_progress "Creating storage link" "docker-compose exec app php artisan storage:link"
            fi

            echo -e "${GREEN}✅ Storage link created.${NC}"
        else
            echo -e "${YELLOW}Operation cancelled.${NC}"
        fi
    fi
}

# Function to run tests
run_tests() {
    echo -e "${BOLD}${BLUE}🧪 Run Tests${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running
    if type is_docker_running &>/dev/null; then
        if ! is_docker_running; then
            echo -e "${RED}Error: Docker is not running. Cannot run tests.${NC}"
            echo -e "${YELLOW}Please start Docker and try again.${NC}"
            return 1
        fi
    elif ! ensure_docker_running; then
        echo -e "${RED}Cannot run tests because Docker is not running.${NC}"
        return 1
    fi

    echo -e "${YELLOW}Available test suites:${NC}"
    echo -e "  ${GREEN}1.${NC} Run all tests"
    echo -e "  ${GREEN}2.${NC} Run unit tests"
    echo -e "  ${GREEN}3.${NC} Run feature tests"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-3]: "
    read -r choice

    case $choice in
        1)
            execute_with_progress "Running all tests" "docker-compose exec app php artisan test"
            ;;
        2)
            execute_with_progress "Running unit tests" "docker-compose exec app php artisan test --testsuite=Unit"
            ;;
        3)
            execute_with_progress "Running feature tests" "docker-compose exec app php artisan test --testsuite=Feature"
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to run Node/NPM commands
run_npm_command() {
    echo -e "${BOLD}${BLUE}📦 Node/NPM Commands${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running
    if type is_docker_running &>/dev/null; then
        if ! is_docker_running; then
            echo -e "${RED}Error: Docker is not running. Cannot run NPM commands.${NC}"
            echo -e "${YELLOW}Please start Docker and try again.${NC}"
            return 1
        fi
    elif ! ensure_docker_running; then
        echo -e "${RED}Cannot run NPM commands because Docker is not running.${NC}"
        return 1
    fi

    echo -e "${YELLOW}Available commands:${NC}"
    echo -e "  ${GREEN}1.${NC} npm install"
    echo -e "  ${GREEN}2.${NC} npm run dev"
    echo -e "  ${GREEN}3.${NC} npm run watch"
    echo -e "  ${GREEN}4.${NC} npm run production"
    echo -e "  ${GREEN}5.${NC} Custom command"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-5]: "
    read -r choice

    case $choice in
        1)
            execute_with_progress "Installing dependencies" "docker-compose exec nodejs npm install"
            ;;
        2)
            execute_with_progress "Running dev build" "docker-compose exec nodejs npm run dev"
            ;;
        3)
            execute_with_progress "Starting watch mode" "docker-compose exec nodejs npm run watch"
            ;;
        4)
            execute_with_progress "Running production build" "docker-compose exec nodejs npm run production"
            ;;
        5)
            echo -ne "${CYAN}Enter npm command:${NC} "
            read -r cmd
            if [ -n "$cmd" ]; then
                execute_with_progress "Running command" "docker-compose exec nodejs npm $cmd"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to run Composer commands
run_composer_command() {
    echo -e "${BOLD}${BLUE}🎼 Composer Commands${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running
    if type is_docker_running &>/dev/null; then
        if ! is_docker_running; then
            echo -e "${RED}Error: Docker is not running. Cannot run Composer commands.${NC}"
            echo -e "${YELLOW}Please start Docker and try again.${NC}"
            return 1
        fi
    elif ! ensure_docker_running; then
        echo -e "${RED}Cannot run Composer commands because Docker is not running.${NC}"
        return 1
    fi

    echo -e "${YELLOW}Available commands:${NC}"
    echo -e "  ${GREEN}1.${NC} composer install"
    echo -e "  ${GREEN}2.${NC} composer update"
    echo -e "  ${GREEN}3.${NC} composer dump-autoload"
    echo -e "  ${GREEN}4.${NC} Custom command"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-4]: "
    read -r choice

    case $choice in
        1)
            execute_with_progress "Installing dependencies" "docker-compose exec app composer install"
            ;;
        2)
            execute_with_progress "Updating dependencies" "docker-compose exec app composer update"
            ;;
        3)
            execute_with_progress "Dumping autoload" "docker-compose exec app composer dump-autoload"
            ;;
        4)
            echo -ne "${CYAN}Enter composer command:${NC} "
            read -r cmd
            if [ -n "$cmd" ]; then
                execute_with_progress "Running command" "docker-compose exec app composer $cmd"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to view resource usage
view_resource_usage() {
    echo -e "${BOLD}${BLUE}📊 Resource Usage${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running for container stats
    DOCKER_RUNNING=false
    if type is_docker_running &>/dev/null; then
        if is_docker_running; then
            DOCKER_RUNNING=true
        fi
    elif command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        DOCKER_RUNNING=true
    fi

    if [ "$DOCKER_RUNNING" = true ]; then
        echo -e "${YELLOW}Container resource usage:${NC}"
        docker stats --no-stream
    else
        echo -e "${YELLOW}Docker is not running. Cannot show container resource usage.${NC}"
    fi

    echo -e "\n${YELLOW}System resource usage:${NC}"
    if command -v top &> /dev/null; then
        top -bn1 | head -n 10
    fi

    if command -v free &> /dev/null; then
        echo -e "\n${YELLOW}Memory usage:${NC}"
        free -h
    fi

    if command -v df &> /dev/null; then
        echo -e "\n${YELLOW}Disk usage:${NC}"
        df -h
    fi
}

# Function to clean Node.js files
clean_nodejs_files() {
    echo -e "${BOLD}${BLUE}🧹 Clean Node.js Files${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running for Docker operations
    DOCKER_RUNNING=false
    if type is_docker_running &>/dev/null; then
        if is_docker_running; then
            DOCKER_RUNNING=true
        else
            echo -e "${YELLOW}Warning: Docker is not running. Some operations may not be available.${NC}"
        fi
    elif command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        DOCKER_RUNNING=true
    else
        echo -e "${YELLOW}Warning: Docker is not running. Some operations may not be available.${NC}"
    fi

    echo -e "${YELLOW}Choose what to clean:${NC}"
    echo -e "  ${GREEN}1.${NC} Clean node_modules"
    echo -e "  ${GREEN}2.${NC} Clean npm cache"
    echo -e "  ${GREEN}3.${NC} Clean both"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-3]: "
    read -r choice

    case $choice in
        1)
            if [ "$DOCKER_RUNNING" = true ]; then
                # Use centralized function if available
                if type docker_nodejs_exec &>/dev/null; then
                    execute_with_progress "Removing node_modules" "docker_nodejs_exec 'cd /var/www && rm -rf node_modules' ''"
                else
                    # Fallback to direct Docker commands
                    execute_with_progress "Removing node_modules" "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf node_modules'"
                fi
            else
                echo -e "${RED}Cannot remove node_modules because Docker is not running.${NC}"
                echo -e "${YELLOW}Please start Docker and try again.${NC}"
                return 1
            fi
            echo -e "${GREEN}✅ node_modules removed.${NC}"
            ;;
        2)
            if [ "$DOCKER_RUNNING" = true ]; then
                execute_with_progress "Clearing npm cache" "docker-compose exec nodejs npm cache clean --force"
            else
                echo -e "${RED}Cannot clear npm cache because Docker is not running.${NC}"
                echo -e "${YELLOW}Please start Docker and try again.${NC}"
                return 1
            fi
            echo -e "${GREEN}✅ npm cache cleared.${NC}"
            ;;
        3)
            if [ "$DOCKER_RUNNING" = true ]; then
                # Use centralized function if available
                if type docker_nodejs_exec &>/dev/null; then
                    execute_with_progress "Removing node_modules" "docker_nodejs_exec 'cd /var/www && rm -rf node_modules' ''"
                else
                    # Fallback to direct Docker commands
                    execute_with_progress "Removing node_modules" "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf node_modules'"
                fi
                # Use centralized function if available
                if type docker_nodejs_exec &>/dev/null; then
                    execute_with_progress "Clearing npm cache" "docker_nodejs_exec 'cd /var/www && npm cache clean --force' ''"
                else
                    # Fallback to direct Docker commands
                    execute_with_progress "Clearing npm cache" "docker-compose exec nodejs sh -c 'cd /var/www && npm cache clean --force'"
                fi
            else
                echo -e "${RED}Cannot remove node_modules and clear npm cache because Docker is not running.${NC}"
                echo -e "${YELLOW}Please start Docker and try again.${NC}"
                return 1
            fi
            echo -e "${GREEN}✅ Node.js files cleaned.${NC}"
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to clean vendor folder
clean_vendor_folder() {
    echo -e "${BOLD}${BLUE}🧹 Clean Vendor Folder${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Choose what to clean:${NC}"
    echo -e "  ${GREEN}1.${NC} Clean vendor folder"
    echo -e "  ${GREEN}2.${NC} Clean Composer cache"
    echo -e "  ${GREEN}3.${NC} Clean both"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-3]: "
    read -r choice

    case $choice in
        1)
            execute_with_progress "Removing vendor folder" "docker-compose exec app rm -rf vendor"
            echo -e "${GREEN}✅ vendor folder removed.${NC}"
            ;;
        2)
            execute_with_progress "Clearing Composer cache" "docker-compose exec app composer clear-cache"
            echo -e "${GREEN}✅ Composer cache cleared.${NC}"
            ;;
        3)
            execute_with_progress "Removing vendor folder" "docker-compose exec app rm -rf vendor"
            execute_with_progress "Clearing Composer cache" "docker-compose exec app composer clear-cache"
            echo -e "${GREEN}✅ Vendor folder and Composer cache cleaned.${NC}"
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}

# Function to handle Laravel management menu (11-16)
handle_laravel_management() {
    local choice=$1

    case $choice in
        11)
            # PHP/Artisan Commands
            run_artisan_command
            ;;
        12)
            # Rebuild App Dependencies (vendor)
            rebuild_app_vendor
            ;;
        13)
            # Fix Storage Link
            fix_storage_link
            ;;
        14)
            # Clean Vendor Folder
            clean_vendor_folder
            ;;
        15)
            # Rebuild Everything (Full)
            build_ui_assets
            ;;
        16)
            # Setup Wizard
            run_setup_wizard
            ;;
        *)
            echo -e "${RED}Invalid Laravel management option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

# Function to handle React management menu (17-21)
handle_react_management() {
    local choice=$1

    case $choice in
        17)
            # Build Manager UI (Production)
            build_manager_ui
            ;;
        18)
            # Dev Mode Manager UI
            "${SCRIPT_DIR}/ru-dev-manager.sh"
            ;;
        19)
            # Node/NPM Commands
            run_npm_command
            ;;
        20)
            # Clean Node.js Files
            clean_nodejs_files
            ;;
        21)
            # Fix NPM Vulnerabilities
            fix_npm_vulnerabilities
            ;;
        *)
            echo -e "${RED}Invalid React management option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

# Function to handle backup management menu (22-23)
handle_backup_management() {
    local choice=$1

    case $choice in
        22)
            # Professional Backup System
            "${SCRIPT_DIR}/ru-backup.sh"
            ;;
        23)
            # Restore Backup
            restore_backup
            ;;
        *)
            echo -e "${RED}Invalid backup management option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}



# Development Tools Submenu
show_development_menu() {
    echo -e "\n${BOLD}${BLUE}🛠️ DEVELOPMENT TOOLS${NC}"
    echo -e "${CYAN}────────────────────────────────────────────────────────${NC}"
    echo -e "   ${GREEN}1.${NC} 🧪 Run Tests"
    echo -e "   ${GREEN}2.${NC} 📊 System Monitor"
    echo -e "   ${GREEN}3.${NC} 📋 View Logs"
    echo -e "   ${GREEN}4.${NC} 🧹 Clear Cache"
    echo -e "   ${GREEN}5.${NC} 📝 Git Operations"
    echo -e "   ${GREEN}6.${NC} 🔧 Execute Custom Command"
    echo -e "   ${GREEN}0.${NC} ⬅️ Back to Main Menu"
    echo -e "${CYAN}────────────────────────────────────────────────────────${NC}"
}

# Function to handle development tools menu (25)
handle_development_tools() {
    local choice=$1

    if [ "$choice" = "25" ]; then
        # Show development tools submenu
        while true; do
            show_development_menu
            read -p "Select development tool [0-6]: " dev_choice

            case $dev_choice in
                1)
                    run_tests
                    ;;
                2)
                    if [ -f "${SCRIPT_DIR}/ru-docker-monitor.sh" ]; then
                        "${SCRIPT_DIR}/ru-docker-monitor.sh"
                    else
                        echo -e "${YELLOW}System monitor not available${NC}"
                    fi
                    ;;
                3)
                    view_logs
                    ;;
                4)
                    clear_cache
                    ;;
                5)
                    git_operations
                    ;;
                6)
                    execute_custom_command
                    ;;
                0)
                    return 0
                    ;;
                *)
                    echo -e "${RED}Invalid option. Please select 0-6.${NC}"
                    ;;
            esac

            if [ "$dev_choice" != "0" ]; then
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
            fi
        done
    fi

    return 0
}

# Network Management Submenu - Modern Design
show_network_menu() {
    echo -e "\n${BOLD}${MAGENTA}▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓${NC}"
    echo -e "${BOLD}${MAGENTA}█${NC}                  ${BOLD}${WHITE}🌐 NETWORK & CONNECTIVITY MANAGEMENT${NC}                  ${BOLD}${MAGENTA}█${NC}"
    echo -e "${BOLD}${MAGENTA}▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓${NC}"

    echo -e "\n${BOLD}${CYAN}▌${NC}${BOLD}${WHITE} NETWORK OPERATIONS ${NC}${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}                                                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 1${NC}   📝 Update Hosts File                                      ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 2${NC}   🔍 Fix DNS Issues                                         ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 3${NC}   🌐 Setup Static Hosts                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 4${NC}   🔧 Network Diagnostics                                    ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${GREEN}▶ 5${NC}   📊 Show Network Status                                    ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}  ${RED}▶ 0${NC}   ⬅️ Back to Main Menu                                       ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌${NC}                                                                     ${BOLD}${CYAN}▐${NC}"
    echo -e "${BOLD}${CYAN}▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌▌${NC}"
}

# Function to handle network management menu (26)
handle_network_management() {
    local choice=$1

    if [ "$choice" = "26" ]; then
        # Show network submenu
        while true; do
            show_network_menu
            read -p "Select network option [0-5]: " network_choice

            case $network_choice in
                1)
                    update_hosts_file
                    ;;
                2)
                    fix_dns_issues
                    ;;
                3)
                    setup_static_hosts
                    ;;
                4)
                    run_network_diagnostics
                    ;;
                5)
                    show_network_status
                    ;;
                0)
                    return 0
                    ;;
                *)
                    echo -e "${RED}Invalid option. Please select 0-5.${NC}"
                    ;;
            esac

            if [ "$network_choice" != "0" ]; then
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
            fi
        done
    fi

    return 0
}

# Network diagnostic function - COMPREHENSIVE TESTING
run_network_diagnostics() {
    echo -e "${BLUE}${BOLD}🔧 Network Diagnostics${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Running comprehensive network diagnostics...${NC}"

    # Test DNS resolution with multiple domains
    echo -e "\n${CYAN}DNS Resolution Tests:${NC}"
    local dns_domains=(
        "josephmatino.com"
        "majokdeng.com"
        "github.com"
        "google.com"
        "cloudflare.com"
    )

    local dns_success=0
    for domain in "${dns_domains[@]}"; do
        echo -ne "${YELLOW}Testing DNS for $domain... ${NC}"
        if nslookup "$domain" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ OK${NC}"
            ((dns_success++))
        else
            echo -e "${RED}❌ FAILED${NC}"
        fi
    done

    echo -e "${CYAN}DNS Results: ${GREEN}$dns_success/${#dns_domains[@]}${NC} domains resolved"

    # Test internet connectivity with multiple IPs
    echo -e "\n${CYAN}Internet Connectivity Tests:${NC}"
    local test_ips=("*******" "*******" "**************")
    local ping_success=0

    for ip in "${test_ips[@]}"; do
        echo -ne "${YELLOW}Testing connectivity to $ip... ${NC}"
        if ping -c 2 -W 3 "$ip" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ OK${NC}"
            ((ping_success++))
        else
            echo -e "${RED}❌ FAILED${NC}"
        fi
    done

    echo -e "${CYAN}Connectivity Results: ${GREEN}$ping_success/${#test_ips[@]}${NC} IPs reachable"

    # Show network interfaces
    echo -e "\n${CYAN}Network Interfaces:${NC}"
    if command -v ip >/dev/null 2>&1; then
        ip addr show | grep -E "(inet |UP|DOWN)" | head -20
    elif command -v ifconfig >/dev/null 2>&1; then
        ifconfig | grep -E "(inet |UP|DOWN)" | head -20
    else
        echo -e "${YELLOW}Network interface tools not available${NC}"
    fi

    # Overall status
    echo -e "\n${CYAN}Overall Network Status:${NC}"
    if [ $dns_success -gt 0 ] && [ $ping_success -gt 0 ]; then
        echo -e "${GREEN}✅ Network connectivity is functional${NC}"
        return 0
    else
        echo -e "${RED}❌ Network connectivity issues detected${NC}"
        return 1
    fi
}

# Show network status
show_network_status() {
    echo -e "${BLUE}${BOLD}📊 Network Status${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Show Docker networks
    echo -e "\n${CYAN}Docker Networks:${NC}"
    docker network ls

    # Show container network info
    echo -e "\n${CYAN}Container Network Status:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

}

# Update hosts file function
update_hosts_file() {
    echo -e "${BLUE}${BOLD}📝 Update Hosts File${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Current hosts file entries:${NC}"
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        # Windows
        cat /c/Windows/System32/drivers/etc/hosts | grep -v "^#" | grep -v "^$"
    else
        # Linux/macOS
        cat /etc/hosts | grep -v "^#" | grep -v "^$"
    fi

    echo -e "\n${CYAN}Add new host entry:${NC}"
    echo -ne "${YELLOW}Enter IP address: ${NC}"
    read -r ip_address
    echo -ne "${YELLOW}Enter hostname: ${NC}"
    read -r hostname

    if [[ -n "$ip_address" && -n "$hostname" ]]; then
        local hosts_entry="$ip_address    $hostname"
        echo -e "${YELLOW}Adding: $hosts_entry${NC}"

        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
            # Windows - requires admin privileges
            echo "$hosts_entry" >> /c/Windows/System32/drivers/etc/hosts
        else
            # Linux/macOS
            echo "$hosts_entry" | sudo tee -a /etc/hosts
        fi

        echo -e "${GREEN}✅ Host entry added successfully${NC}"
    else
        echo -e "${RED}❌ Invalid input. Both IP and hostname required.${NC}"
    fi
}

# Fix DNS issues function - PROPER IMPLEMENTATION
fix_dns_issues() {
    echo -e "${BLUE}${BOLD}🔍 Fix DNS Issues${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Testing DNS resolution with real domains...${NC}"

    # Test domains - NO HARDCODING, real domains only
    local test_domains=(
        "josephmatino.com"
        "majokdeng.com"
        "github.com"
        "google.com"
        "cloudflare.com"
    )

    local failed_domains=()
    local success_count=0

    echo -e "\n${CYAN}DNS Resolution Tests:${NC}"
    for domain in "${test_domains[@]}"; do
        echo -ne "${YELLOW}Testing $domain... ${NC}"
        if nslookup "$domain" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ OK${NC}"
            ((success_count++))
        else
            echo -e "${RED}❌ FAILED${NC}"
            failed_domains+=("$domain")
        fi
    done

    echo -e "\n${CYAN}Results: ${GREEN}$success_count/${#test_domains[@]}${NC} domains resolved successfully"

    if [ ${#failed_domains[@]} -gt 0 ]; then
        echo -e "${RED}❌ Failed domains: ${failed_domains[*]}${NC}"

        echo -e "\n${YELLOW}Attempting to fix DNS issues:${NC}"

        # Flush DNS cache
        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
            echo -e "${CYAN}Flushing Windows DNS cache...${NC}"
            ipconfig /flushdns
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            echo -e "${CYAN}Flushing macOS DNS cache...${NC}"
            sudo dscacheutil -flushcache
        else
            echo -e "${CYAN}Restarting Linux DNS resolver...${NC}"
            sudo systemctl restart systemd-resolved 2>/dev/null || sudo service networking restart 2>/dev/null
        fi

        echo -e "${GREEN}✅ DNS cache flushed${NC}"

        # Test failed domains again
        echo -e "\n${CYAN}Re-testing failed domains:${NC}"
        local fixed_count=0
        for domain in "${failed_domains[@]}"; do
            echo -ne "${YELLOW}Re-testing $domain... ${NC}"
            if nslookup "$domain" >/dev/null 2>&1; then
                echo -e "${GREEN}✅ FIXED${NC}"
                ((fixed_count++))
            else
                echo -e "${RED}❌ STILL FAILED${NC}"
            fi
        done

        if [ $fixed_count -gt 0 ]; then
            echo -e "${GREEN}✅ Fixed $fixed_count DNS issues${NC}"
        else
            echo -e "${YELLOW}⚠️ DNS issues persist. Consider:${NC}"
            echo -e "   ${CYAN}• Changing DNS servers to ******* or *******${NC}"
            echo -e "   ${CYAN}• Checking firewall settings${NC}"
            echo -e "   ${CYAN}• Verifying internet connection${NC}"
        fi
    else
        echo -e "${GREEN}✅ All DNS tests passed - no issues found${NC}"
    fi
}

# Setup static hosts function - PROPER IMPLEMENTATION
setup_static_hosts() {
    echo -e "${BLUE}${BOLD}🌐 Setup Static Hosts${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if environment variables are set
    if [ -z "${APP_URL}" ] || [ -z "${MANAGER_URL}" ]; then
        echo -e "${RED}❌ ERROR: APP_URL and MANAGER_URL must be set in .env file${NC}"
        echo -e "${YELLOW}Please configure your .env file with proper URLs${NC}"
        return 1
    fi

    echo -e "${YELLOW}Setting up static hosts from environment configuration...${NC}"

    # Extract hostnames from environment URLs - NO FALLBACKS
    local app_host=$(echo "${APP_URL}" | sed 's|http[s]*://||' | cut -d':' -f1)
    local manager_host=$(echo "${MANAGER_URL}" | sed 's|http[s]*://||' | cut -d':' -f1)

    # Validate extracted hosts
    if [ -z "$app_host" ] || [ -z "$manager_host" ]; then
        echo -e "${RED}❌ ERROR: Could not extract hostnames from URLs${NC}"
        echo -e "${YELLOW}APP_URL: ${APP_URL}${NC}"
        echo -e "${YELLOW}MANAGER_URL: ${MANAGER_URL}${NC}"
        return 1
    fi

    echo -e "\n${CYAN}Environment-based host entries:${NC}"
    echo -e "${GREEN}127.0.0.1    $app_host${NC} ${YELLOW}(from APP_URL)${NC}"
    echo -e "${GREEN}127.0.0.1    $manager_host${NC} ${YELLOW}(from MANAGER_URL)${NC}"

    # Additional development hosts from environment
    echo -e "\n${CYAN}Additional development hosts (from environment):${NC}"
    if [ -n "${ADDITIONAL_HOSTS}" ]; then
        # Parse ADDITIONAL_HOSTS from .env (comma-separated)
        IFS=',' read -ra HOSTS <<< "${ADDITIONAL_HOSTS}"
        for host in "${HOSTS[@]}"; do
            host=$(echo "$host" | xargs) # trim whitespace
            echo -e "${GREEN}127.0.0.1    $host${NC}"
        done
    else
        echo -e "${YELLOW}⚠️ No ADDITIONAL_HOSTS defined in .env${NC}"
        echo -e "${YELLOW}Add ADDITIONAL_HOSTS=hcare.local,carecloud.local to .env${NC}"
    fi

    echo -e "\n${YELLOW}Do you want to add these entries to your hosts file? (y/n): ${NC}"
    read -r confirm

    if [[ "$confirm" == [Yy]* ]]; then
        local hosts_file
        if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
            hosts_file="/c/Windows/System32/drivers/etc/hosts"
        else
            hosts_file="/etc/hosts"
        fi

        # Array of all hosts to add
        local hosts_to_add=(
            "$app_host"
            "$manager_host"
            "hcare.local"
            "carecloud.local"
            "manager.hcare.local"
            "manager.carecloud.local"
        )

        # Add entries if they don't exist
        for host in "${hosts_to_add[@]}"; do
            if ! grep -q "$host" "$hosts_file" 2>/dev/null; then
                echo "127.0.0.1    $host" | sudo tee -a "$hosts_file" >/dev/null
                echo -e "${GREEN}✅ Added: 127.0.0.1    $host${NC}"
            else
                echo -e "${YELLOW}⚠️ Already exists: $host${NC}"
            fi
        done

        echo -e "${GREEN}✅ Static hosts setup completed${NC}"
    else
        echo -e "${YELLOW}Static hosts setup cancelled${NC}"
    fi
}

# Function to build UI assets
build_ui_assets() {
    # Clear the screen for better visibility
    clear

    echo -e "\n${BOLD}${BLUE}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${BLUE}║                  🎨 BUILD UI ASSETS                             ║${NC}"
    echo -e "${BOLD}${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}\n"

    # Check if Docker is running
    if ! ensure_docker_running; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    fi

    # First check if required services are running
    # Try to detect the NodeJS container name if not set
    if [ -z "${NODEJS_CONTAINER_NAME}" ]; then
        if docker ps 2>/dev/null | grep -q "hcarecloud-nodejs.*Up"; then
            NODEJS_CONTAINER_NAME="hcarecloud-nodejs"
            echo -e "${CYAN}Detected NodeJS container: ${NODEJS_CONTAINER_NAME}${NC}"
        elif docker ps 2>/dev/null | grep -q "nodejs.*Up"; then
            NODEJS_CONTAINER_NAME="nodejs"
            echo -e "${CYAN}Detected NodeJS container: ${NODEJS_CONTAINER_NAME}${NC}"
        else
            # Default fallback
            NODEJS_CONTAINER_NAME="hcarecloud-nodejs"
            echo -e "${YELLOW}Could not detect NodeJS container, using default: ${NODEJS_CONTAINER_NAME}${NC}"
        fi
    fi

    # Export the NODEJS_CONTAINER_NAME so it's available to all functions and subprocesses
    export NODEJS_CONTAINER_NAME

    # Check if NodeJS container is running using both docker-compose and Docker CLI
    local nodejs_running=false

    # Try docker-compose first
    if docker-compose ps 2>/dev/null | grep -q "${NODEJS_CONTAINER_NAME}.*Up"; then
        nodejs_running=true
        echo -e "${GREEN}NodeJS container is running via docker-compose${NC}"
    # Then try Docker CLI
    elif docker ps 2>/dev/null | grep -q "${NODEJS_CONTAINER_NAME}.*Up"; then
        nodejs_running=true
        echo -e "${GREEN}NodeJS container is running via Docker CLI${NC}"
    # Try with nodejs name if NODEJS_CONTAINER_NAME has hcarecloud- prefix
    elif [[ "${NODEJS_CONTAINER_NAME}" == hcarecloud-* ]] && (docker-compose ps 2>/dev/null | grep -q "nodejs.*Up" || docker ps 2>/dev/null | grep -q "nodejs.*Up"); then
        nodejs_running=true
        NODEJS_CONTAINER_NAME="nodejs"
        echo -e "${GREEN}NodeJS container is running as 'nodejs'${NC}"
    # Try with hcarecloud-nodejs if NODEJS_CONTAINER_NAME is just nodejs
    elif [[ "${NODEJS_CONTAINER_NAME}" == "nodejs" ]] && (docker-compose ps 2>/dev/null | grep -q "hcarecloud-nodejs.*Up" || docker ps 2>/dev/null | grep -q "hcarecloud-nodejs.*Up"); then
        nodejs_running=true
        NODEJS_CONTAINER_NAME="hcarecloud-nodejs"
        echo -e "${GREEN}NodeJS container is running as 'hcarecloud-nodejs'${NC}"
    fi

    if [ "$nodejs_running" = false ]; then
        echo -e "${YELLOW}NodeJS container is not running. This is required for building UI assets.${NC}\n"
        echo -e "${YELLOW}Would you like to try starting the NodeJS container? (y/n)${NC}"
        read -r start_nodejs
        if [[ "$start_nodejs" == [Yy]* ]]; then
            echo -e "${YELLOW}Trying to start NodeJS container...${NC}"
            if docker-compose up -d nodejs 2>/dev/null || docker start "${NODEJS_CONTAINER_NAME}" 2>/dev/null || docker start "nodejs" 2>/dev/null || docker start "hcarecloud-nodejs" 2>/dev/null; then
                echo -e "${GREEN}✓ NodeJS container started successfully.${NC}\n"
            else
                echo -e "${RED}Failed to start NodeJS container.${NC}\n"
                echo -e "${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            fi
        else
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi
    else
        echo -e "${GREEN}✓ NodeJS container is running.${NC}\n"
    fi

    echo -e "${BOLD}${YELLOW}Available Build Options:${NC}"
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${NC} ${BOLD}${CYAN}Main Application:${NC}                                            ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}1.${NC} Development build (Main App)                               ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}2.${NC} Production build (Main App)                               ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}3.${NC} Install npm dependencies (Main App)                        ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}                                                                  ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} ${BOLD}${CYAN}Web Manager UI:${NC}                                             ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}4.${NC} Build Web Manager UI (Development)                        ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}5.${NC} Build Web Manager UI (Production)                         ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}6.${NC} Install npm dependencies (Web Manager)                     ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}                                                                  ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} ${BOLD}${CYAN}Combined Operations:${NC}                                        ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}7.${NC} Rebuild Everything (App + Manager)                        ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}8.${NC} Clean and reinstall all dependencies                      ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}9.${NC} Fix common build issues                                   ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}                                                                  ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} ${BOLD}${CYAN}After UI Changes:${NC}                                           ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}10.${NC} Rebuild Web Manager UI assets only                        ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}                                                                  ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC}  ${GREEN}0.${NC} Cancel                                                    ${BLUE}║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"

    echo -ne "\n${CYAN}Enter your choice${NC} [0-10]: "
    read -r build_choice

    case $build_choice in
        1|2)
            # Main app build
            echo -e "\n${BLUE}╔═ Main App $([ $build_choice -eq 1 ] && echo 'Development' || echo 'Production') Build ═══════════════════════════════╗${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot build.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            if [ $build_choice -eq 1 ]; then
                execute_with_progress "Building development assets" "docker-compose exec nodejs npm run dev"
                echo -e "${GREEN}✓ Development assets built successfully.${NC}"
            else
                execute_with_progress "Building production assets" "docker-compose exec nodejs npm run production"
                echo -e "${GREEN}✓ Production assets built successfully.${NC}"
            fi
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
            ;;
        3)
            # Install npm dependencies for main app
            echo -e "\n${BLUE}╔═ Installing npm dependencies (Main App) ═════════════════════════╗${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot install dependencies.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            execute_with_progress "Installing npm dependencies" "docker-compose exec nodejs npm install"
            echo -e "${GREEN}✓ Main app npm dependencies installed successfully.${NC}"
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
            ;;
        4|5)
            # Web manager UI build
            echo -e "\n${BLUE}╔═ Web Manager UI ($([ $build_choice -eq 4 ] && echo 'Development' || echo 'Production')) ══════════════════════════════╗${NC}"

            # Check if ru-build-manager-ui.sh exists
            if [ -f "${SCRIPT_DIR}/ru-build-manager-ui.sh" ]; then
                # Export the NODEJS_CONTAINER_NAME variable so ru-build-manager-ui.sh can use it
                export NODEJS_CONTAINER_NAME="${NODEJS_CONTAINER_NAME}"

                # Run the build script with production flag if option 5 was selected
                echo -e "${CYAN}Running ru-build-manager-ui.sh with NODEJS_CONTAINER_NAME=${NODEJS_CONTAINER_NAME}${NC}"

                # Use a safer execution method with error handling
                if ! "${SCRIPT_DIR}/ru-build-manager-ui.sh" $([ $build_choice -eq 5 ] && echo 'prod'); then
                    echo -e "${RED}Error: The ru-build-manager-ui.sh script encountered an issue.${NC}"
                    echo -e "${YELLOW}Attempting to run with fallback method...${NC}"

                    # Try with explicit sourcing which can be more stable
                    cd "${SCRIPT_DIR}"
                    source "./ru-build-manager-ui.sh" $([ $build_choice -eq 5 ] && echo 'prod')

                    if [ $? -ne 0 ]; then
                        echo -e "${RED}Error: Both execution methods failed.${NC}"
                        echo -e "${YELLOW}Attempting direct npm commands as a last resort...${NC}"

                        # Try direct npm commands as a last resort
                        if [ $build_choice -eq 5 ]; then
                            execute_with_progress \
                                "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install --include=dev --legacy-peer-deps && npx vite build --mode production'" \
                                "Building Web Manager UI (Production)" \
                                30 \
                                true
                        else
                            execute_with_progress \
                                "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install --include=dev --legacy-peer-deps && npx vite build'" \
                                "Building Web Manager UI (Development)" \
                                30 \
                                true
                        fi
                    fi
                fi

                # Check the exit code
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✓ Web Manager UI build completed successfully.${NC}"

                    # Ask if user wants to restart the web manager container
                    echo -e "${YELLOW}Would you like to restart the web manager container to apply changes? (y/n)${NC}"
                    read -r restart_manager
                    if [[ "$restart_manager" == [Yy]* ]]; then
                        echo -e "${CYAN}Restarting web manager container...${NC}"
                        if docker-compose restart manager 2>/dev/null || docker restart hcarecloud-manager 2>/dev/null || docker restart manager 2>/dev/null; then
                            echo -e "${GREEN}✓ Web manager container restarted successfully.${NC}"
                        else
                            echo -e "${YELLOW}Could not restart web manager container automatically.${NC}"
                            echo -e "${YELLOW}You may need to restart it manually.${NC}"
                        fi
                    fi
                else
                    echo -e "${RED}✗ Web Manager UI build failed.${NC}"
                    echo -e "${YELLOW}Check the error messages above for details.${NC}"
                fi
            else
                echo -e "${RED}Error: ru-build-manager-ui.sh not found!${NC}"
                echo -e "${YELLOW}This script is required to build the Web Manager UI.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            fi
            ;;
        # End of case 4|5
        6)
            # Install npm dependencies for web manager
            echo -e "\n${BLUE}╔═ Installing npm dependencies (Web Manager) ═══════════════════════╗${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot install dependencies.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            # Use the execute_with_progress function which has proper fallback mechanisms
            execute_with_progress \
                "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install'" \
                "Installing npm dependencies for Web Manager" \
                20 \
                true

            # Check if the command was successful
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✓ Web Manager npm dependencies installed successfully.${NC}"
            else
                echo -e "${RED}✗ Failed to install Web Manager npm dependencies.${NC}"
                echo -e "${YELLOW}Please check if the NodeJS container is running and the path is correct.${NC}"

                # Try with explicit container names as a last resort
                echo -e "${YELLOW}Attempting with explicit container names as a last resort...${NC}"

                if docker exec hcarecloud-nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install' 2>/dev/null; then
                    echo -e "${GREEN}✓ Dependencies installed successfully with Docker CLI (hcarecloud-nodejs)${NC}"
                elif docker exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install' 2>/dev/null; then
                    echo -e "${GREEN}✓ Dependencies installed successfully with Docker CLI (nodejs)${NC}"
                else
                    echo -e "${RED}✗ All attempts to install dependencies failed.${NC}"
                    echo -e "${YELLOW}Please ensure the NodeJS container is running and properly configured.${NC}"
                fi
            fi
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
            ;;
        7)
            # Build both main app and web manager UI
            echo -e "\n${BLUE}╔═ Building Everything (App + Manager) ═══════════════════════════╗${NC}"

            # 1. First build main app in production mode
            echo -e "${CYAN}Step 1: Building main app in production mode...${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot build.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            execute_with_progress "Building main app assets" "docker-compose exec nodejs npm run production"
            echo -e "${GREEN}✓ Main app assets built successfully.${NC}"

            # 2. Then build web manager in production mode
            echo -e "\n${CYAN}Step 2: Building web manager UI in production mode...${NC}"
            if [ -f "${SCRIPT_DIR}/ru-build-manager-ui.sh" ]; then
                # Call ru-build-manager-ui.sh in production mode
                "${SCRIPT_DIR}/ru-build-manager-ui.sh" prod
            else
                echo -e "${RED}Error: ru-build-manager-ui.sh not found!${NC}"
                echo -e "${YELLOW}Attempting to build web manager UI directly...${NC}"
                execute_with_progress "Installing manager npm dependencies" "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install --include=dev --legacy-peer-deps'"
                execute_with_progress "Building manager theme" "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npx vite build'"
            fi

            echo -e "${GREEN}✓ Complete rebuild finished.${NC}"
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
            ;;
        8)
            # Clean and reinstall all dependencies
            echo -e "\n${BLUE}╔═ Clean and Reinstall All Dependencies ═════════════════════════╗${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot clean and reinstall.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            echo -e "${CYAN}Step 1: Cleaning main app node_modules...${NC}"
            # Use centralized function if available
            if type docker_nodejs_exec &>/dev/null; then
                execute_with_progress "Removing node_modules" "docker_nodejs_exec 'cd /var/www && rm -rf node_modules' ''"
            else
                # Fallback to direct Docker commands
                execute_with_progress "Removing node_modules" "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf node_modules'"
            fi

            echo -e "${CYAN}Step 2: Cleaning web manager node_modules...${NC}"
            # Use centralized function if available
            if type docker_nodejs_exec &>/dev/null; then
                execute_with_progress "Removing web manager node_modules" "docker_nodejs_exec 'cd /var/www && cd docker/scripts/hcc_management/themes/hcc_themeData && rm -rf node_modules' ''"
            else
                # Fallback to direct Docker commands
                execute_with_progress "Removing web manager node_modules" "docker-compose exec nodejs sh -c 'cd /var/www && cd docker/scripts/hcc_management/themes/hcc_themeData && rm -rf node_modules'"
            fi

            echo -e "${CYAN}Step 3: Reinstalling main app dependencies...${NC}"
            # Use centralized function if available
            if type docker_nodejs_exec &>/dev/null; then
                execute_with_progress "Installing main app npm dependencies" "docker_nodejs_exec 'cd /var/www && npm install' ''"
            else
                # Fallback to direct Docker commands
                execute_with_progress "Installing main app npm dependencies" "docker-compose exec nodejs sh -c 'cd /var/www && npm install'"
            fi

            echo -e "${CYAN}Step 4: Reinstalling web manager dependencies...${NC}"
            # Use centralized function if available
            if type docker_nodejs_exec &>/dev/null; then
                execute_with_progress "Installing web manager npm dependencies" "docker_nodejs_exec 'cd /var/www && cd docker/scripts/hcc_management/themes/hcc_themeData && npm install' ''"
            else
                # Fallback to direct Docker commands
                execute_with_progress "Installing web manager npm dependencies" "docker-compose exec nodejs sh -c 'cd /var/www && cd docker/scripts/hcc_management/themes/hcc_themeData && npm install'"
            fi

            echo -e "${GREEN}✓ All dependencies cleaned and reinstalled successfully.${NC}"
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
            ;;
        9)
            # Fix common build issues
            echo -e "\n${BLUE}╔═ Fix Common Build Issues ═══════════════════════════════════════╗${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot fix build issues.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            echo -e "${CYAN}Step 1: Checking for missing dependencies in main app...${NC}"
            execute_with_progress "Checking main app dependencies" "docker-compose exec nodejs npm ls --json 2>/dev/null | grep -o '\"missing\"' | wc -l"

            echo -e "${CYAN}Step 2: Checking for missing dependencies in web manager...${NC}"
            execute_with_progress "Checking web manager dependencies" "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm ls --json 2>/dev/null | grep -o \"missing\" | wc -l'"

            echo -e "${CYAN}Step 3: Fixing package-lock.json issues...${NC}"
            execute_with_progress "Fixing main app package-lock.json" "docker-compose exec nodejs npm install --package-lock-only"
            execute_with_progress "Fixing web manager package-lock.json" "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install --package-lock-only'"

            echo -e "${CYAN}Step 4: Installing missing dependencies...${NC}"
            execute_with_progress "Installing main app missing dependencies" "docker-compose exec nodejs npm install"
            execute_with_progress "Installing web manager missing dependencies" "docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npm install'"

            echo -e "${GREEN}✓ Common build issues fixed successfully.${NC}"
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
            ;;
        0)
            echo -e "${YELLOW}Operation cancelled.${NC}"
            ;;
        10)
            # Rebuild Web Manager UI assets only (after UI changes)
            echo -e "\n${BLUE}╔═ Rebuilding Web Manager UI Assets Only ═════════════════════╗${NC}"
            ensure_container_running "hcarecloud-nodejs" || {
                echo -e "${RED}Error: NodeJS container is not running. Cannot rebuild assets.${NC}"
                echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            }

            echo -e "${CYAN}Rebuilding Web Manager UI assets (without reinstalling dependencies)...${NC}"
            docker-compose exec nodejs sh -c 'cd /var/www/docker/scripts/hcc_management/themes/hcc_themeData && npx vite build'
            echo -e "${GREEN}✓ Web Manager UI assets rebuilt successfully.${NC}"
            echo -e "${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}"

            # Ask if the web manager container should be restarted
            echo -ne "\n${YELLOW}Would you like to restart the web manager container to apply changes? (y/n):${NC} "
            read -r restart_manager
            if [[ "$restart_manager" == [Yy]* ]]; then
                echo -e "${CYAN}Restarting web manager container...${NC}"
                docker-compose restart manager
                echo -e "${GREEN}✓ Web manager container restarted successfully.${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid choice. Please enter a number between 0 and 10.${NC}"
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
}

# Function to rebuild app vendor directory
rebuild_app_vendor() {
    if [ -f "${SCRIPT_DIR}/ru-rebuild-app-vendor.sh" ]; then
        "${SCRIPT_DIR}/ru-rebuild-app-vendor.sh"
    else
        echo -e "${RED}Error: ru-rebuild-app-vendor.sh not found${NC}"
        echo -e "${YELLOW}This script rebuilds the main app vendor directory using Composer.${NC}"
        echo -e "${YELLOW}Attempting to rebuild vendor directory directly...${NC}"
        execute_with_progress "Installing dependencies" "docker-compose exec app composer install"
        return 1
    fi
}

# Function to build web manager UI only
build_manager_ui() {
    if [ -f "${SCRIPT_DIR}/ru-build-manager-ui.sh" ]; then
        "${SCRIPT_DIR}/ru-build-manager-ui.sh"
    else
        echo -e "${RED}Error: ru-build-manager-ui.sh not found${NC}"
        echo -e "${YELLOW}This script builds only the web manager UI.${NC}"
        echo -e "${YELLOW}Attempting to build web manager UI directly...${NC}"
        execute_with_progress "Installing npm dependencies" "docker-compose exec nodejs bash -c 'cd /app/docker/scripts/hcc_management/themes/hcc_themeData && npm install --include=dev --legacy-peer-deps'"
        execute_with_progress "Building theme" "docker-compose exec nodejs bash -c 'cd /app/docker/scripts/hcc_management/themes/hcc_themeData && npx vite build'"
        return 1
    fi
}

# Function to show UI/Node tools submenu
show_ui_menu() {
    local refresh_submenu=true
    local submenu_choice

    while true; do
        if [ "$refresh_submenu" = true ]; then
            if [ "$CLEAR_SCREEN" = "true" ]; then
                clear
            fi

            echo -e "${BLUE}${BOLD}╭───────────────────────────────────────────────────────╮${NC}"
            echo -e "${BLUE}${BOLD}│${NC} ${CYAN}${BOLD}UI/Node.js Tools${NC}${BLUE}${BOLD} │${NC}"
            echo -e "${BLUE}${BOLD}╰───────────────────────────────────────────────────────╯${NC}\n"

            echo -e "${YELLOW}Select an option:${NC}"
            echo -e "  ${GREEN}1.${NC} Build UI assets (development)"
            echo -e "  ${GREEN}2.${NC} Build UI assets (production)"
            echo -e "  ${GREEN}3.${NC} Watch UI assets for changes"
            echo -e "  ${GREEN}4.${NC} Run UI tests"
            echo -e "  ${GREEN}5.${NC} Run npm command"
            echo -e "  ${GREEN}6.${NC} Manage node_modules"
            echo -e "  ${GREEN}0.${NC} Back to main menu"

            refresh_submenu=false
        fi

        echo -ne "${CYAN}Enter your choice${NC} [0-6]: "
        read -r submenu_choice

        case $submenu_choice in
            0)
                return
                ;;
            1)
                echo -e "${BLUE}Building UI assets in development mode...${NC}"
                if check_prerequisites "ui"; then
                    docker-compose exec nodejs npm run dev
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            2)
                echo -e "${BLUE}Building UI assets in production mode...${NC}"
                if check_prerequisites "ui"; then
                    docker-compose exec nodejs npm run prod
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            3)
                echo -e "${BLUE}Watching UI assets for changes...${NC}"
                echo -e "${YELLOW}Press Ctrl+C to stop watching.${NC}"
                if check_prerequisites "ui"; then
                    docker-compose exec nodejs npm run watch
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            4)
                echo -e "${BLUE}Running UI tests...${NC}"
                if check_prerequisites "ui"; then
                    docker-compose exec nodejs npm test
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            5)
                echo -ne "${CYAN}Enter npm command:${NC} "
                read -r npm_command
                if [ -n "$npm_command" ]; then
                    if check_prerequisites "ui"; then
                        echo -e "${BLUE}Running:${NC} npm $npm_command"
                        docker-compose exec nodejs npm $npm_command
                    fi
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            6)
                echo -e "${BLUE}Node.js module management:${NC}"
                echo -e "  ${GREEN}1.${NC} Install all modules (npm install)"
                echo -e "  ${GREEN}2.${NC} Install specific module"
                echo -e "  ${GREEN}3.${NC} Remove specific module"
                echo -e "  ${GREEN}4.${NC} Update all modules"
                echo -e "  ${GREEN}5.${NC} List installed modules"
                echo -e "  ${GREEN}0.${NC} Back"

                echo -ne "${CYAN}Select option:${NC} "
                read -r module_option

                case $module_option in
                    1)
                        if check_prerequisites "ui"; then
                            echo -e "${BLUE}Installing all modules...${NC}"
                            docker-compose exec nodejs npm install
                        fi
                        ;;
                    2)
                        echo -ne "${CYAN}Enter module name:${NC} "
                        read -r module_name
                        if [ -n "$module_name" ]; then
                            if check_prerequisites "ui"; then
                                echo -e "${BLUE}Installing module:${NC} $module_name"
                                docker-compose exec nodejs npm install $module_name
                            fi
                        fi
                        ;;
                    3)
                        echo -ne "${CYAN}Enter module name:${NC} "
                        read -r module_name
                        if [ -n "$module_name" ]; then
                            if check_prerequisites "ui"; then
                                echo -e "${BLUE}Removing module:${NC} $module_name"
                                docker-compose exec nodejs npm uninstall $module_name
                            fi
                        fi
                        ;;
                    4)
                        if check_prerequisites "ui"; then
                            echo -e "${BLUE}Updating all modules...${NC}"
                            docker-compose exec nodejs npm update
                        fi
                        ;;
                    5)
                        if check_prerequisites "ui"; then
                            echo -e "${BLUE}Installed modules:${NC}"
                            docker-compose exec nodejs npm list --depth=0
                        fi
                        ;;
                    0)
                        ;;
                    *)
                        echo -e "${RED}Invalid option.${NC}"
                        ;;
                esac
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            *)
                echo -e "${RED}Invalid option. Please try again.${NC}"
                sleep 1
                ;;
        esac
    done
}

# Function to show tests menu
show_tests_menu() {
    local refresh_submenu=true
    local submenu_choice

    while true; do
        if [ "$refresh_submenu" = true ]; then
            if [ "$CLEAR_SCREEN" = "true" ]; then
                clear
            fi

            echo -e "${BLUE}${BOLD}╭───────────────────────────────────────────────────────╮${NC}"
            echo -e "${BLUE}${BOLD}│${NC} ${CYAN}${BOLD}Test Runner${NC}${BLUE}${BOLD} │${NC}"
            echo -e "${BLUE}${BOLD}╰───────────────────────────────────────────────────────╯${NC}\n"

            echo -e "${YELLOW}Select a test option:${NC}"
            echo -e "  ${GREEN}1.${NC} Run all tests"
            echo -e "  ${GREEN}2.${NC} Run PHP unit tests"
            echo -e "  ${GREEN}3.${NC} Run frontend tests"
            echo -e "  ${GREEN}4.${NC} Run feature tests"
            echo -e "  ${GREEN}5.${NC} Run specific test suite"
            echo -e "  ${GREEN}6.${NC} Run specific test file"
            echo -e "  ${GREEN}0.${NC} Back to main menu"

            refresh_submenu=false
        fi

        echo -ne "${CYAN}Enter your choice${NC} [0-6]: "
        read -r submenu_choice

        case $submenu_choice in
            0)
                return
                ;;
            1)
                echo -e "${BLUE}Running all tests...${NC}"
                if check_prerequisites "artisan"; then
                    docker-compose exec app vendor/bin/phpunit
                fi
                if check_prerequisites "ui"; then
                    docker-compose exec nodejs npm test
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            2)
                if check_prerequisites "artisan"; then
                    echo -e "${BLUE}Running PHP Unit tests...${NC}"
                    docker-compose exec app vendor/bin/phpunit
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            3)
                if check_prerequisites "ui"; then
                    echo -e "${BLUE}Running frontend tests...${NC}"
                    docker-compose exec nodejs npm test
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            4)
                if check_prerequisites "artisan"; then
                    echo -e "${BLUE}Running feature tests...${NC}"
                    docker-compose exec app vendor/bin/phpunit --testsuite=Feature
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            5)
                if check_prerequisites "artisan"; then
                    echo -e "${BLUE}Available test suites:${NC}"
                    echo -e "  - Unit"
                    echo -e "  - Feature"
                    echo -e "  - Integration"

                    echo -ne "${CYAN}Enter test suite name:${NC} "
                    read -r suite_name

                    if [ -n "$suite_name" ]; then
                        echo -e "${BLUE}Running test suite:${NC} $suite_name"
                        docker-compose exec app vendor/bin/phpunit --testsuite=$suite_name
                    else
                        echo -e "${RED}Test suite name cannot be empty.${NC}"
                    fi
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            6)
                if check_prerequisites "artisan"; then
                    echo -e "${BLUE}Available test files:${NC}"
                    docker-compose exec app find tests -name "*Test.php" | sort

                    echo -ne "${CYAN}Enter test file path (relative to project root):${NC} "
                    read -r test_file

                    if [ -n "$test_file" ]; then
                        echo -e "${BLUE}Running test:${NC} $test_file"
                        docker-compose exec app vendor/bin/phpunit $test_file
                    else
                        echo -e "${RED}Test file path cannot be empty.${NC}"
                    fi
                fi
                echo -e "\n${YELLOW}Press Enter to continue...${NC}"
                read -r
                refresh_submenu=true
                ;;
            *)
                echo -e "${RED}Invalid option. Please try again.${NC}"
                sleep 1
                ;;
        esac
    done
}

# Function to toggle maintenance mode
toggle_maintenance_mode() {
    echo -e "${BOLD}${BLUE}🔧 Maintenance Mode${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    if ! check_prerequisites "artisan"; then
        return 1
    fi

    # Check current maintenance mode status
    local is_down=$(docker-compose exec -T app php artisan isdown)

    if [[ "$is_down" == *"Application is in maintenance mode"* ]]; then
        echo -e "${YELLOW}Application is currently in maintenance mode.${NC}"
        echo -e "${YELLOW}Do you want to disable maintenance mode? (y/n)${NC}"
        read -r disable_maintenance

        if [[ "$disable_maintenance" == [Yy]* ]]; then
            echo -e "${BLUE}Disabling maintenance mode...${NC}"
            docker-compose exec app php artisan up
            echo -e "${GREEN}✅ Maintenance mode disabled.${NC}"
        else
            echo -e "${YELLOW}Maintenance mode remains enabled.${NC}"
        fi
    else
        echo -e "${GREEN}Application is currently live.${NC}"
        echo -e "${YELLOW}Do you want to enable maintenance mode? (y/n)${NC}"
        read -r enable_maintenance

        if [[ "$enable_maintenance" == [Yy]* ]]; then
            echo -e "${YELLOW}Enter a custom message (optional):${NC}"
            read -r maintenance_message

            if [ -n "$maintenance_message" ]; then
                echo -e "${BLUE}Enabling maintenance mode with custom message...${NC}"
                docker-compose exec app php artisan down --message="$maintenance_message"
            else
                echo -e "${BLUE}Enabling maintenance mode...${NC}"
                docker-compose exec app php artisan down
            fi
            echo -e "${GREEN}✅ Maintenance mode enabled.${NC}"
        else
            echo -e "${GREEN}Application remains live.${NC}"
        fi
    fi
}

# Function to clear cache
clear_cache() {
    echo -e "${BOLD}${BLUE}🧹 Cache Management${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Select cache to clear:${NC}"
    echo -e "  ${GREEN}1.${NC} Clear all caches"
    echo -e "  ${GREEN}2.${NC} Clear application cache"
    echo -e "  ${GREEN}3.${NC} Clear configuration cache"
    echo -e "  ${GREEN}4.${NC} Clear route cache"
    echo -e "  ${GREEN}5.${NC} Clear view cache"
    echo -e "  ${GREEN}6.${NC} Clear compiled classes"
    echo -e "  ${GREEN}7.${NC} Clear npm cache"
    echo -e "  ${GREEN}8.${NC} Clear Docker build cache"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-8]: "
    read -r cache_choice

    case $cache_choice in
        0)
            return 0
            ;;
        1)
            echo -e "${BLUE}Clearing all caches...${NC}"
            execute_with_progress "Clearing application cache" \
                "docker-compose exec app php artisan cache:clear"
            execute_with_progress "Clearing configuration cache" \
                "docker-compose exec app php artisan config:clear"
            execute_with_progress "Clearing route cache" \
                "docker-compose exec app php artisan route:clear"
            execute_with_progress "Clearing view cache" \
                "docker-compose exec app php artisan view:clear"
            execute_with_progress "Optimizing class autoloader" \
                "docker-compose exec app composer dump-autoload -o"

            if check_prerequisites "ui"; then
                execute_with_progress "Clearing npm cache" \
                    "docker-compose exec nodejs npm cache clean --force"
            fi

            echo -e "${GREEN}✅ All caches cleared successfully.${NC}"
            ;;
        2)
            execute_with_progress "Clearing application cache" \
                "docker-compose exec app php artisan cache:clear"
            ;;
        3)
            execute_with_progress "Clearing configuration cache" \
                "docker-compose exec app php artisan config:clear"
            ;;
        4)
            execute_with_progress "Clearing route cache" \
                "docker-compose exec app php artisan route:clear"
            ;;
        5)
            execute_with_progress "Clearing view cache" \
                "docker-compose exec app php artisan view:clear"
            ;;
        6)
            execute_with_progress "Clearing compiled classes" \
                "docker-compose exec app php artisan clear-compiled"
            ;;
        7)
            if check_prerequisites "ui"; then
                execute_with_progress "Clearing npm cache" \
                    "docker-compose exec nodejs npm cache clean --force"
            else
                echo -e "${RED}NodeJS container not running.${NC}"
            fi
            ;;
        8)
            echo -e "${RED}Warning: This will remove all Docker build cache.${NC}"
            echo -e "${YELLOW}This may significantly increase the time required for your next build.${NC}"
            read -p "Are you sure you want to continue? (yes/no): " confirm

            if [[ "$confirm" == "yes" ]]; then
                execute_with_progress "Clearing Docker build cache" \
                    "docker builder prune -f"
                echo -e "${GREEN}Docker build cache cleared.${NC}"
            else
                echo -e "${YELLOW}Operation canceled.${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option. Please try again.${NC}"
            ;;
    esac
}

# Function to execute custom command
execute_custom_command() {
    echo -e "${BOLD}${BLUE}⚙️ Execute Custom Command${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Select container to run command in:${NC}"
    echo -e "  ${GREEN}1.${NC} App container (PHP/Laravel)"
    echo -e "  ${GREEN}2.${NC} Database container"
    echo -e "  ${GREEN}3.${NC} Web server container"
    echo -e "  ${GREEN}4.${NC} Node.js container"
    echo -e "  ${GREEN}5.${NC} Redis container"
    echo -e "  ${GREEN}6.${NC} Manager container"
    echo -e "  ${GREEN}7.${NC} Custom container"
    echo -e "  ${GREEN}8.${NC} Host system (bash)"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-8]: "
    read -r container_choice

    case $container_choice in
        0)
            return 0
            ;;
        1)
            echo -ne "${CYAN}Enter command to run in app container:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing in app container:${NC} $command"
                docker-compose exec app bash -c "$command"
            fi
            ;;
        2)
            echo -ne "${CYAN}Enter command to run in database container:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing in database container:${NC} $command"
                docker-compose exec db bash -c "$command"
            fi
            ;;
        3)
            echo -ne "${CYAN}Enter command to run in web server container:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing in web server container:${NC} $command"
                docker-compose exec nginx bash -c "$command"
            fi
            ;;
        4)
            echo -ne "${CYAN}Enter command to run in Node.js container:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing in Node.js container:${NC} $command"
                docker-compose exec nodejs sh -c "$command"
            fi
            ;;
        5)
            echo -ne "${CYAN}Enter command to run in Redis container:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing in Redis container:${NC} $command"
                docker-compose exec redis bash -c "$command"
            fi
            ;;
        6)
            echo -ne "${CYAN}Enter command to run in Manager container:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing in Manager container:${NC} $command"
                docker-compose exec manager bash -c "$command"
            fi
            ;;
        7)
            echo -e "${BLUE}Available containers:${NC}"
            docker-compose ps --services

            echo -ne "${CYAN}Enter container name:${NC} "
            read -r container_name

            if [ -n "$container_name" ]; then
                echo -ne "${CYAN}Enter command to run:${NC} "
                read -r command

                if [ -n "$command" ]; then
                    echo -e "${BLUE}Executing in $container_name:${NC} $command"
                    docker-compose exec $container_name bash -c "$command"
                fi
            fi
            ;;
        8)
            echo -ne "${CYAN}Enter command to run on host system:${NC} "
            read -r command
            if [ -n "$command" ]; then
                echo -e "${BLUE}Executing on host:${NC} $command"
                bash -c "$command"
            fi
            ;;
        *)
            echo -e "${RED}Invalid option. Please try again.${NC}"
            ;;
    esac
}

# Function to open web UI
open_web_ui() {
    echo -e "${BOLD}${BLUE}🌐 Open Web Interface${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Select interface to open:${NC}"
    echo -e "  ${GREEN}1.${NC} Main application"
    echo -e "  ${GREEN}2.${NC} Manager interface"
    echo -e "  ${GREEN}3.${NC} phpMyAdmin"
    echo -e "  ${GREEN}4.${NC} Custom URL"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-4]: "
    read -r ui_choice

    case $ui_choice in
        0)
            return 0
            ;;
        1)
            echo -e "${BLUE}Opening main application...${NC}"
            if command -v xdg-open &> /dev/null; then
                xdg-open "${APP_URL}" &
            elif command -v open &> /dev/null; then
                open "${APP_URL}"
            else
                echo -e "${YELLOW}Please open this URL in your browser:${NC} ${APP_URL}"
            fi
            ;;
        2)
            echo -e "${BLUE}Opening manager interface...${NC}"
            if command -v xdg-open &> /dev/null; then
                xdg-open "${MANAGER_URL}" &
            elif command -v open &> /dev/null; then
                open "${MANAGER_URL}"
            else
                echo -e "${YELLOW}Please open this URL in your browser:${NC} ${MANAGER_URL}"
            fi
            ;;
        3)
            echo -e "${BLUE}Opening phpMyAdmin...${NC}"
            if command -v xdg-open &> /dev/null; then
                xdg-open "${PHPMYADMIN_URL}" &
            elif command -v open &> /dev/null; then
                open "${PHPMYADMIN_URL}"
            else
                echo -e "${YELLOW}Please open this URL in your browser:${NC} ${PHPMYADMIN_URL}"
            fi
            ;;
        4)
            echo -ne "${CYAN}Enter URL to open:${NC} "
            read -r custom_url
            if [ -n "$custom_url" ]; then
                echo -e "${BLUE}Opening custom URL:${NC} $custom_url"
                if command -v xdg-open &> /dev/null; then
                    xdg-open "$custom_url" &
                elif command -v open &> /dev/null; then
                    open "$custom_url"
                else
                    echo -e "${YELLOW}Please open this URL in your browser:${NC} $custom_url"
                fi
            fi
            ;;
        *)
            echo -e "${RED}Invalid option. Please try again.${NC}"
            ;;
    esac
}

# Function to show status
show_status() {
    clear
    echo -e "\n${BOLD}${BLUE}╔═══════════════════ SYSTEM STATUS ══════════════════════╗${NC}"
    echo -e "${BLUE}║                                                          ║${NC}"

    # Docker Status
    if systemctl is-active --quiet docker; then
        echo -e "${BLUE}║${NC} ${GREEN}✓ Docker Service:${NC} Running                               ${BLUE}║${NC}"
    else
        echo -e "${BLUE}║${NC} ${RED}✗ Docker Service:${NC} Not Running                            ${BLUE}║${NC}"
    fi
    echo -e "${BLUE}║                                                          ║${NC}"
    echo -e "${BLUE}╠══════════════════ CONTAINERS ═══════════════════════════╣${NC}"
    echo -e "${BLUE}║                                                          ║${NC}"

    # Core Services
    echo -e "${BLUE}║${NC} ${CYAN}Core Services:${NC}                                          ${BLUE}║${NC}"
    format_status_line "app" "Main Application" "9000"
    format_status_line "db" "Database" "3306"
    format_status_line "nginx" "Web Server" "80,8080-8084"
    echo -e "${BLUE}║                                                          ║${NC}"

    # Support Services
    echo -e "${BLUE}║${NC} ${CYAN}Support Services:${NC}                                       ${BLUE}║${NC}"
    format_status_line "redis" "Cache Server" "6379"
    format_status_line "nodejs" "Node.js" "-"
    echo -e "${BLUE}║                                                          ║${NC}"

    # Management Tools
    echo -e "${BLUE}║${NC} ${CYAN}Management Tools:${NC}                                       ${BLUE}║${NC}"
    format_status_line "manager" "Web Manager" "6600"
    format_status_line "pma" "phpMyAdmin" "8080"

    echo -e "${BLUE}║                                                          ║${NC}"
    echo -e "${BLUE}╠═════════════════ RESOURCE USAGE ═════════════════════════╣${NC}"
    echo -e "${BLUE}║                                                          ║${NC}"

    # Resource Usage Table
    printf "${BLUE}║${NC} %-12s %-8s %-20s %-14s ${BLUE}║${NC}\n" "CONTAINER" "CPU%" "MEMORY" "NETWORK I/O"
    echo -e "${BLUE}║${NC} ────────────────────────────────────────────────── ${BLUE}║${NC}"

    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" | while read -r line; do
        if [[ $line != *"NAME"* ]]; then
            container=$(echo "$line" | awk '{print $1}' | sed 's/hcarecloud-//')
            cpu=$(echo "$line" | awk '{print $2}')
            mem=$(echo "$line" | awk '{print $3 "/" $5}')
            net=$(echo "$line" | awk '{print $7 "/" $9}')
            printf "${BLUE}║${NC} %-12s %-8s %-20s %-14s ${BLUE}║${NC}\n" "$container" "$cpu" "$mem" "$net"
        fi
    done

    echo -e "${BLUE}║                                                          ║${NC}"
    echo -e "${BLUE}╠═════════════════ QUICK ACCESS ══════════════════════════╣${NC}"
    echo -e "${BLUE}║                                                          ║${NC}"
    echo -e "${BLUE}║${NC} ${YELLOW}Main App${NC}    → ${APP_URL}                      ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} ${YELLOW}Web Manager${NC} → ${MANAGER_URL}                    ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} ${YELLOW}phpMyAdmin${NC}  → ${PHPMYADMIN_URL}                    ${BLUE}║${NC}"
    echo -e "${BLUE}║                                                          ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════╝${NC}\n"
}

# Helper function to format status lines
format_status_line() {
    local name="hcarecloud-$1"
    local service="$2"
    local port="$3"
    local status
    local color
    local symbol
    local status_text

    status=$(docker ps -a --filter "name=$name" --format "{{.Status}}" 2>/dev/null)

    if [[ $status == *"Up"* && $status == *"healthy"* ]]; then
        color=$GREEN
        symbol="✓"
        status_text="Healthy"
    elif [[ $status == *"Up"* ]]; then
        color=$YELLOW
        symbol="!"
        status_text="Running"
    else
        color=$RED
        symbol="✗"
        status_text="Stopped"
    fi

    printf "${BLUE}║${NC} ${color}%s${NC} %-15s %-12s %-15s ${BLUE}║${NC}\n" "$symbol" "$service" "$status_text" "Port: $port"
}

# Main menu handler
handle_main_menu() {
    local choice=$1

    case $choice in
        1)
            start_all_services
            ;;
        2)
            stop_all_services
            ;;
        3)
            restart_all_services
            ;;
        4)
            show_status  # Using our new status function
            ;;
        *)
            echo -e "${RED}Invalid main menu option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-dev.sh"
    exit 1
fi

# Function to start all services
start_all_services() {
    echo -e "${BOLD}${BLUE}⚡ Starting All Services${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # List of core services in order of dependency
    local services=(
        "db"
        "redis"
        "app"
        "nodejs"
        "nginx"
        "manager"
        "pma"
    )

    # Start each service
    for service in "${services[@]}"; do
        echo -e "${YELLOW}Starting $service...${NC}"
        execute_with_progress "Starting $service container" "docker-compose up -d $service"

        # Wait for container to be ready
        echo -ne "${YELLOW}Waiting for $service to be ready...${NC}"
        for i in {1..30}; do
            if check_container_running "hcarecloud-$service"; then
                echo -e "${GREEN}✓${NC}"
                break
            fi
            echo -n "."
            sleep 1
            if [ $i -eq 30 ]; then
                echo -e "\n${RED}Error: $service container failed to start${NC}"
                return 1
            fi
        done
    done

    echo -e "${GREEN}✅ All services started successfully${NC}"
    return 0
}

# Export all functions to make them available to other scripts
export -f run_artisan_command
export -f manage_database
export -f fix_storage_link
export -f run_tests
export -f run_npm_command
export -f run_composer_command
export -f view_resource_usage
export -f clean_nodejs_files
export -f clean_vendor_folder
export -f handle_development_tools
export -f build_ui_assets
export -f build_manager_ui
export -f rebuild_app_vendor