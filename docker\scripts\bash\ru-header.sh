#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Header Layout Script                               ║
# ║                                                                          ║
# ║ This script provides the header layout for the H-CareCloud management    ║
# ║ system. It is used by runall.sh to display the header.                   ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Colors for output (in case they're not inherited)
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to display the header
display_header() {
    # Get system information
    local os_info=$1
    local kernel_version=$2
    local cpu_info=$3
    local memory_info=$4
    local docker_status=$5
    local docker_type=$6
    local docker_version=$7
    local containers_up=$8
    local total_containers=$9

    # Get environment status with proper color
    local env_status="${CURRENT_ENV:-local}"
    local env_color
    case "$env_status" in
        production) env_color="${RED}" ;;
        staging) env_color="${YELLOW}" ;;
        development) env_color="${GREEN}" ;;
        *) env_color="${MAGENTA}" ;;
    esac

    # Determine Docker status with proper icon and color
    local docker_icon="⚠️"
    local docker_status_color="${RED}"
    if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        docker_icon="✅"
        docker_status_color="${GREEN}"
    fi

    # Display ASCII logo
    echo -e "\n${BLUE}${BOLD}"
    echo -e "  _    _         _____                _____ _                 _ "
    echo -e " | |  | |       / ____|              / ____| |               | |"
    echo -e " | |__| |______| |     __ _ _ __ ___| |    | | ___  _   _  __| |"
    echo -e " |  __  |______| |    / _\` | '__/ _ \ |    | |/ _ \| | | |/ _\` |"
    echo -e " | |  | |      | |___| (_| | | |  __/ |____| | (_) | |_| | (_| |"
    echo -e " |_|  |_|       \_____\__,_|_|  \___|\_____|_|\___/ \__,_|\__,_|"
    echo -e "${NC}"

    # Display modern, professional header with dynamic version from environment
    local app_version="${APP_VERSION}"
    if [ -z "$app_version" ]; then
        echo -e "${RED}ERROR: APP_VERSION not set in environment${NC}"
        return 1
    fi

    echo -e "${BOLD}${CYAN}╔═══════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║${NC}                ${BOLD}${GREEN}H-CARECLOUD MANAGEMENT SYSTEM v${app_version}${NC}                 ${BOLD}${CYAN}║${NC}"
    echo -e "${BOLD}${CYAN}╚═══════════════════════════════════════════════════════════════════════════╝${NC}"

    # Responsive system status section
    echo -e "${BOLD}${CYAN}┌─ SYSTEM STATUS ─┐${NC}"
    echo -e "${BOLD}${CYAN}│${NC} ${BOLD}Environment:${NC} ${env_color}${env_status}${NC}"
    echo -e "${BOLD}${CYAN}│${NC} ${BOLD}Docker:${NC} ${docker_status_color}${docker_icon}${NC}"
    echo -e "${BOLD}${CYAN}│${NC} ${BOLD}Containers:${NC} ${GREEN}${containers_up}/${total_containers}${NC}"
    echo -e "${BOLD}${CYAN}└─────────────────┘${NC}"

    # Team information section with cleaner layout
    echo -e "${BOLD}${MAGENTA}👥 DEVELOPMENT TEAM${NC}"
    echo -e "${BLUE}┌─────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Developer:${NC}    ${GREEN}Joseph Matino${NC} <${CYAN}<EMAIL>${NC}>             ${BLUE}│${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Scrum Master:${NC} ${GREEN}Majok Deng${NC}                                       ${BLUE}│${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Support:${NC}      ${CYAN}<EMAIL>${NC}                            ${BLUE}│${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Website:${NC}      ${CYAN}https://hostwek.com/wekturbo${NC}                      ${BLUE}│${NC}"
    echo -e "${BLUE}└─────────────────────────────────────────────────────────────────────┘${NC}"
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-header.sh"
    return 1
fi
