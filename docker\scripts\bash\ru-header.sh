#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Header Layout Script                               ║
# ║                                                                          ║
# ║ This script provides the header layout for the H-CareCloud management    ║
# ║ system. It is used by runall.sh to display the header.                   ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Colors for output (in case they're not inherited)
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to display the header
display_header() {
    # Get parameters (some may be empty, that's OK)
    local current_env="${1:-development}"
    local docker_status="${2:-Not Running}"
    local containers_up="${3:-0}"
    local total_containers="${4:-0}"
    local docker_type="${5:-Docker Engine}"
    local docker_version="${6:-Unknown}"
    local os_info="${7:-$(uname -s)}"
    local kernel_version="${8:-$(uname -r)}"
    local cpu_info="${9:-Unknown}"
    local memory_info="${10:-Unknown}"

    # Get environment status with proper color
    local env_color
    case "$current_env" in
        production) env_color="${RED}" ;;
        staging) env_color="${YELLOW}" ;;
        development) env_color="${GREEN}" ;;
        *) env_color="${MAGENTA}" ;;
    esac

    # Determine Docker status with proper icon and color
    local docker_icon="⚠️"
    local docker_status_color="${RED}"
    if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        docker_icon="✅"
        docker_status_color="${GREEN}"
        docker_status="Running"
    fi

    # Display ASCII logo (preserve user's logo)
    echo -e "\n${BLUE}${BOLD}"
    echo -e "  _    _         _____                _____ _                 _ "
    echo -e " | |  | |       / ____|              / ____| |               | |"
    echo -e " | |__| |______| |     __ _ _ __ ___| |    | | ___  _   _  __| |"
    echo -e " |  __  |______| |    / _\` | '__/ _ \ |    | |/ _ \| | | |/ _\` |"
    echo -e " | |  | |      | |___| (_| | | |  __/ |____| | (_) | |_| | (_| |"
    echo -e " |_|  |_|       \_____\__,_|_|  \___|\_____|_|\___/ \__,_|\__,_|"
    echo -e "${NC}"

    # Modern header with dynamic version
    local app_version="${APP_VERSION:-1.0.8}"

    echo -e "${BOLD}${CYAN}╔═══════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║${NC}                ${BOLD}${GREEN}H-CARECLOUD MANAGEMENT SYSTEM v${app_version}${NC}                 ${BOLD}${CYAN}║${NC}"
    echo -e "${BOLD}${CYAN}╚═══════════════════════════════════════════════════════════════════════════╝${NC}"

    # Modern system status section
    echo -e "${BOLD}${CYAN}┌─ SYSTEM STATUS ─┐${NC}"
    echo -e "${BOLD}${CYAN}│${NC} Environment: ${env_color}${current_env}${NC}"
    echo -e "${BOLD}${CYAN}│${NC} Docker: ${docker_status_color}${docker_icon}${NC}"
    echo -e "${BOLD}${CYAN}│${NC} Containers: ${GREEN}${containers_up}${NC}/${YELLOW}${total_containers}${NC} ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}└─────────────────┘${NC}"

    # Team information section with modern design
    echo -e "${BOLD}${MAGENTA}👥 DEVELOPMENT TEAM${NC}"
    echo -e "${BLUE}┌─────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Developer:${NC}    ${GREEN}Joseph Matino${NC} <${CYAN}<EMAIL>${NC}>             ${BLUE}│${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Scrum Master:${NC} ${GREEN}Majok Deng${NC}                                       ${BLUE}│${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Support:${NC}      ${CYAN}<EMAIL>${NC}                            ${BLUE}│${NC}"
    echo -e "${BLUE}│${NC} ${BOLD}Website:${NC}      ${CYAN}https://hostwek.com/wekturbo${NC}                      ${BLUE}│${NC}"
    echo -e "${BLUE}└─────────────────────────────────────────────────────────────────────┘${NC}"
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-header.sh"
    return 1
fi
