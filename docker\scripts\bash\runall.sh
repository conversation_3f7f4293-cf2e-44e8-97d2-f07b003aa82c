#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Docker Management System                           ║
# ║                                                                          ║
# ║ This script provides a comprehensive management interface for all Docker ║
# ║ containers in the H-CareCloud healthcare management system. It allows    ║
# ║ for building, starting, and monitoring all services through a unified    ║
# ║ interface.                                                              ║
# ║                                                                          ║
# ║ Key Features:                                                            ║
# ║ • Central version management for all containers                          ║
# ║ • Build and start controls for the entire application stack              ║
# ║ • Integration with Manager web interface for advanced operations         ║
# ║ • Container shell access and log viewing capabilities                    ║
# ║ • Automatic detection of environment variables from .env                 ║
# ║                                                                          ║
# ║ Primary Developer: <PERSON> <<EMAIL>>                  ║
# ║ Scrum Master: Majok Deng                                                 ║
# ║ Support: <EMAIL>                                           ║
# ║ Company: hostwek.com/wekturbo                                            ║
# ║ Version: ${APP_VERSION}                                                  ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source helper functions FIRST - THIS IS REQUIRED
# ru-helper.sh is the definitive source of truth for PROJECT_ROOT
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
    # PROJECT_ROOT will be set by ru-helper.sh
    echo -e "${GREEN}Using centralized helper functions from ru-helper.sh${NC}"

    # Display a separator for better visibility
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${GREEN}H-CareCloud Management System${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    # Verify that PROJECT_ROOT is correctly set
    if [ ! -f "${PROJECT_ROOT}/docker-compose.yml" ]; then
        echo -e "${RED}ERROR: PROJECT_ROOT is set to ${PROJECT_ROOT}, but docker-compose.yml was not found there.${NC}"
        echo -e "${YELLOW}This may indicate an issue with the PROJECT_ROOT determination in ru-helper.sh.${NC}"
        exit 1
    fi
else
    # Colors for output (in case they're not inherited)
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    RED='\033[0;31m'
    BLUE='\033[0;34m'
    CYAN='\033[0;36m'
    NC='\033[0m' # No Color

    echo -e "${RED}ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}"
    echo -e "${RED}This file is required for the management system.${NC}"
    echo -e "${YELLOW}Please ensure ru-helper.sh exists in the same directory as this script.${NC}"
    exit 1
fi

# Load environment variables from .env file
load_env_variables() {
    local env_file="${PROJECT_ROOT}/.env"
    local env_current="${PROJECT_ROOT}/.env.current"

    if [ -f "$env_file" ]; then
        # Load main .env file
        set -a
        source "$env_file"
        set +a

        # Detect current environment - always refresh from .env
        CURRENT_ENV=$(grep "^APP_ENV=" "$env_file" | cut -d= -f2 | tr -d '"' || echo "unknown")
        echo "CURRENT_ENV=${CURRENT_ENV}" > "$env_current"
    else
        CURRENT_ENV="unknown"
        echo "CURRENT_ENV=${CURRENT_ENV}" > "$env_current"
    fi

    # Export the variable so it's available to header script
    export CURRENT_ENV
}

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Default settings
CLEAR_SCREEN=true

# Initialize system variables
OS_INFO=$(uname -s)
KERNEL_VERSION=$(uname -r)
CPU_INFO=$(grep -c processor /proc/cpuinfo 2>/dev/null || echo "Unknown")
MEMORY_INFO=$(free -h 2>/dev/null | awk '/Mem:/ {print $2}' || echo "Unknown")
DOCKER_STATUS=$(docker info &>/dev/null && echo "Running" || echo "Not Running")
DOCKER_TYPE="Docker Engine"
if docker info 2>/dev/null | grep -q "Docker Desktop"; then
    DOCKER_TYPE="Docker Desktop"
fi
DOCKER_VERSION=$(docker --version 2>/dev/null | awk '{print $3}' | tr -d ',' || echo "Unknown")
CONTAINERS_UP=$(docker compose ps 2>/dev/null | grep -c "Up" || echo "0")
TOTAL_CONTAINERS=$(docker compose ps -q 2>/dev/null | wc -l || echo "0")

# Windows detection
IS_WINDOWS=false
WIN_VERSION=""
WIN_CPU=""
WIN_RAM=""
WIN_DISK=""

if [[ "$OS_INFO" == *"MINGW"* ]] || [[ "$OS_INFO" == *"MSYS"* ]] || [[ "$OS_INFO" == *"CYGWIN"* ]]; then
    IS_WINDOWS=true
    WIN_VERSION=$(cmd.exe /c "ver" 2>/dev/null | grep -o "[0-9]\.[0-9]\.[0-9]*" || echo "Unknown")
    WIN_CPU=$(wmic cpu get name 2>/dev/null | grep -v "Name" | head -n 1 | sed 's/^[ \t]*//' || echo "Unknown")
    WIN_RAM=$(wmic computersystem get totalphysicalmemory 2>/dev/null | grep -v "TotalPhysicalMemory" | awk '{printf "%.2f GB", $1/(1024*1024*1024)}' || echo "Unknown")
    WIN_DISK=$(wmic logicaldisk where "DeviceID='C:'" get size,freespace 2>/dev/null | grep -v "Size" | awk '{printf "%.2f GB free of %.2f GB", $1/(1024*1024*1024), $2/(1024*1024*1024)}' || echo "Unknown")
fi

# Load environment variables before anything else
load_env_variables

# Source required scripts in the correct order
source "${SCRIPT_DIR}/ru-version.sh"
source "${SCRIPT_DIR}/ru-functions.sh"

# Source environment management first as it's used by other scripts
source "${SCRIPT_DIR}/ru-env.sh"

# ru-helper.sh has already been sourced at the beginning of this script
# DO NOT source it again to avoid overriding PROJECT_ROOT

# Source UI-related scripts
source "${SCRIPT_DIR}/ru-header.sh"
source "${SCRIPT_DIR}/ru-menu.sh"
source "${SCRIPT_DIR}/ru-help.sh"

# Source functional modules
source "${SCRIPT_DIR}/ru-docker.sh"
source "${SCRIPT_DIR}/ru-dev.sh"
source "${SCRIPT_DIR}/ru-system.sh"
source "${SCRIPT_DIR}/ru-backup.sh"
source "${SCRIPT_DIR}/ru-special.sh"

# Define the build_ui_assets function to use our direct implementation
build_ui_assets() {
    # Use our direct implementation script
    if [ -f "${SCRIPT_DIR}/ru-build-ui.sh" ]; then
        bash "${SCRIPT_DIR}/ru-build-ui.sh"
        return $?
    else
        echo -e "${RED}Error: ru-build-ui.sh not found!${NC}"
        echo -e "${YELLOW}This script is required for building UI assets.${NC}"
        return 1
    fi
}
export -f build_ui_assets

# Source main loop last
source "${SCRIPT_DIR}/ru-main.sh"

# Check if a menu option was passed as a command-line argument
if [ $# -eq 1 ] && [[ $1 =~ ^[0-9]+$ ]]; then
    # If a valid numeric argument was provided, use it as the menu choice
    choice=$1

    # Display the header
    show_menu

    # Process the choice directly
    case $choice in
        # Docker Management (1-10)
        1|2|3|4|5|6|7|8|9|10)
            handle_docker_management $choice
            ;;

        # H-CareCloud Laravel (11-16)
        11|12|13|14|15|16)
            handle_laravel_management $choice
            ;;

        # H-CareManager React (17-21)
        17|18|19|20|21)
            handle_react_management $choice
            ;;

        # Backup & Recovery (22-23)
        22|23)
            handle_backup_management $choice
            ;;

        # Environment Management (24)
        24)
            handle_environment_management $choice
            ;;

        # Development Tools (25)
        25)
            handle_development_tools $choice
            ;;

        # Network & Connectivity (26)
        26)
            handle_network_management $choice
            ;;

        # System & Help (27-28)
        27|28)
            handle_system_help $choice
            ;;

        # Advanced Operations (29-31)
        29|30|31)
            handle_special_operations $choice
            ;;

        *)
            echo -e "${RED}Invalid option: $choice${NC}"
            ;;
    esac
else
    # Start the main loop if no argument was provided
    main_loop
fi