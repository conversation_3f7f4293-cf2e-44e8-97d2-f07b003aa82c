#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud System Operation Functions                         ║
# ║                                                                          ║
# ║ This script provides system operation functions for H-CareCloud.        ║
# ║ It handles monitoring, health checks, and system configuration.         ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Ensure SCRIPT_DIR is defined
if [ -z "${SCRIPT_DIR}" ]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
fi

# Source helper functions first - NO FALLBACKS
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
    echo -e "${CYAN}PROJECT_ROOT: ${PROJECT_ROOT}${NC}"
else
    echo -e "${RED}FATAL ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}" >&2
    return 1
fi

# Verify PROJECT_ROOT is set
if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "${RED}FATAL ERROR: PROJECT_ROOT not set. ru-helper.sh must be sourced properly.${NC}" >&2
    return 1
fi

# Function to handle system help menu (27-28)
handle_system_help() {
    local choice=$1

    case $choice in
        27)
            # Help & Documentation
            if [ -f "${SCRIPT_DIR}/ru-help.sh" ]; then
                source "${SCRIPT_DIR}/ru-help.sh"
                show_help
            else
                echo -e "${RED}Error: Help system not found${NC}"
            fi
            ;;
        28)
            # Show Config Details
            show_config_details
            ;;
        *)
            echo -e "${RED}Invalid system help option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

# Function to show configuration details
show_config_details() {
    echo -e "${BLUE}${BOLD}H-CareCloud Configuration Details${NC}"
    echo -e "${CYAN}PROJECT_ROOT: ${PROJECT_ROOT}${NC}"
    echo -e "${CYAN}SCRIPT_DIR: ${SCRIPT_DIR}${NC}"
    echo -e "${CYAN}Current Environment: ${CURRENT_ENV}${NC}"

    if [ -f "${PROJECT_ROOT}/.env" ]; then
        echo -e "${GREEN}.env file found${NC}"
        echo -e "${YELLOW}Key environment variables:${NC}"
        grep -E "^(APP_ENV|APP_DEBUG|MANAGER_THEME_PATH|MANAGER_PORT)" "${PROJECT_ROOT}/.env" | head -10
    else
        echo -e "${RED}.env file not found${NC}"
    fi
}

# Function to run setup wizard (moved to Laravel section)
run_setup_wizard() {
    echo -e "${BLUE}${BOLD}H-CareCloud Setup Wizard${NC}"
    echo -e "${YELLOW}This will guide you through the initial setup...${NC}"
    echo -e "${CYAN}Setting up Laravel application...${NC}"

    # Run Laravel setup commands
    echo -e "${GREEN}1. Generating application key...${NC}"
    docker exec app php artisan key:generate --force

    echo -e "${GREEN}2. Running migrations...${NC}"
    docker exec app php artisan migrate --force

    echo -e "${GREEN}3. Seeding database...${NC}"
    docker exec app php artisan db:seed --force

    echo -e "${GREEN}4. Creating storage link...${NC}"
    docker exec app php artisan storage:link

    echo -e "${GREEN}5. Clearing caches...${NC}"
    docker exec app php artisan config:clear
    docker exec app php artisan cache:clear
    docker exec app php artisan view:clear

    echo -e "${BOLD}${GREEN}Setup wizard completed successfully!${NC}"
}

# Source common functions if not already sourced
if ! type execute_with_progress &>/dev/null; then
    if [ -f "${SCRIPT_DIR}/ru-functions.sh" ]; then
        source "${SCRIPT_DIR}/ru-functions.sh"
    else
        echo -e "${RED}FATAL ERROR: ru-functions.sh not found at ${SCRIPT_DIR}/ru-functions.sh${NC}" >&2
        return 1
    fi
fi

# Function to monitor system resources
monitor_system() {
    echo -e "${BOLD}${BLUE}📊 System Monitor${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    if ! ensure_docker_running; then
        echo -e "${RED}Cannot monitor system because Docker is not running.${NC}"

        # Show system resources even if Docker is not running
        echo -e "\n${BOLD}${BLUE}═══════════ SYSTEM RESOURCES ═══════════${NC}\n"
        echo -e "${CYAN}CPU Usage:${NC}"
        top -bn1 | head -n 5

        echo -e "\n${CYAN}Memory Usage:${NC}"
        free -h

        echo -e "\n${CYAN}Disk Usage:${NC}"
        df -h | grep -v tmpfs

        return 1
    fi

    # Display a more informative header
    echo -e "${BOLD}${BLUE}═══════════ DOCKER CONTAINER STATISTICS ═══════════${NC}\n"
    docker stats --no-stream

    echo -e "\n${BOLD}${BLUE}═══════════ DISK USAGE ═══════════${NC}\n"
    docker system df

    echo -e "\n${BOLD}${BLUE}═══════════ CONTAINER HEALTH ═══════════${NC}\n"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Health}}"

    echo -e "\n${BOLD}${BLUE}═══════════ SYSTEM RESOURCES ═══════════${NC}\n"
    echo -e "${CYAN}CPU Usage:${NC}"
    top -bn1 | head -n 5

    echo -e "\n${CYAN}Memory Usage:${NC}"
    free -h

    echo -e "\n${CYAN}Disk Usage:${NC}"
    df -h | grep -v tmpfs

    return 0
}

# Function to show configuration details
show_config_details() {
    echo -e "${BOLD}${BLUE}⚙️ Configuration Details${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Docker Version:${NC}"
    docker version --format 'Docker Version: {{.Server.Version}}'

    echo -e "\n${YELLOW}Docker Compose Version:${NC}"
    docker-compose version

    echo -e "\n${YELLOW}Environment:${NC}"
    if [ -f "${PROJECT_ROOT}/.env" ]; then
        grep -E "^(APP_|DB_|REDIS_|MAIL_)" "${PROJECT_ROOT}/.env" | sort
    fi

    echo -e "\n${YELLOW}Services Configuration:${NC}"
    docker-compose config --services

    echo -e "\n${YELLOW}Network Configuration:${NC}"
    docker network ls | grep "hcarecloud"
}

# Function to run setup wizard
run_setup_wizard() {
    # Clear the screen for better visibility
    clear

    echo -e "\n${BOLD}${BLUE}╔════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${BLUE}║                  🧙 H-CARECLOUD SETUP WIZARD                  ║${NC}"
    echo -e "${BOLD}${BLUE}╚════════════════════════════════════════════════════════════════╝${NC}\n"

    echo -e "${YELLOW}This wizard will help you set up the main H-CareCloud application.${NC}"
    echo -e "${YELLOW}It will configure the environment, database, and install dependencies.${NC}\n"

    # Check if Docker is running
    if ! ensure_docker_running; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    else
        echo -e "${GREEN}✅ Docker is running.${NC}"
    fi

    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        echo -e "${RED}Error: docker-compose is not installed!${NC}"
        echo -e "${YELLOW}Please install docker-compose and try again.${NC}\n"
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    else
        echo -e "${GREEN}✅ Docker Compose is installed.${NC}"
    fi

    # Check if containers are running
    if ! ensure_services_running; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    else
        echo -e "${GREEN}✅ Docker containers are running.${NC}"
    fi

    # Step 1: Environment Configuration
    echo -e "\n${BOLD}${GREEN}Step 1: Environment Configuration${NC}"

    # Check if app container is running
    if ! ensure_container_running "app"; then
        echo -e "${YELLOW}Press Enter to continue...${NC}"
        read -r
        return 1
    fi

    # Check if .env file exists in the app container
    if check_docker_file "/var/www/.env" "app"; then
        echo -e "${GREEN}✅ .env file exists in the Docker container.${NC}"
    else
        echo -e "${YELLOW}Checking for .env.example file...${NC}"

        # Find .env.example file
        local env_example_path=$(find_file ".env.example")

        if [[ "$env_example_path" == DOCKER:* ]]; then
            # .env.example found in Docker container
            local docker_path=${env_example_path#DOCKER:}
            echo -e "${YELLOW}Found .env.example in Docker container. Creating .env file...${NC}"
            docker-compose exec -T app cp "$docker_path" "/var/www/.env"
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✅ .env file created in Docker container.${NC}"
            else
                echo -e "${RED}❌ Failed to create .env file in Docker container.${NC}"
                echo -e "${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            fi
        elif [ -n "$env_example_path" ]; then
            # .env.example found locally
            echo -e "${YELLOW}Found .env.example at $env_example_path. Copying to Docker container...${NC}"
            docker cp "$env_example_path" "$(docker-compose ps -q app):/var/www/.env"
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✅ .env file created in Docker container.${NC}"
            else
                echo -e "${RED}❌ Failed to copy .env.example to Docker container.${NC}"
                echo -e "${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            fi
        else
            echo -e "${RED}❌ No .env.example file found in Docker container or project root.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi
    fi

    # Configure environment settings
    echo -e "${YELLOW}Would you like to configure environment settings? (y/n)${NC}"
    read -r configure_env
    if [[ "$configure_env" == [Yy]* ]]; then
        # App name
        read -p "Application name [H-CareCloud]: " app_name
        app_name=${app_name:-H-CareCloud}

        # App URL
        read -p "Application URL [${APP_URL:-http://localhost:8000}]: " app_url
        app_url=${app_url:-${APP_URL:-http://localhost:8000}}

        # App environment
        echo -e "${YELLOW}Select environment:${NC}"
        echo -e "1) Production (recommended)"
        echo -e "2) Development"
        echo -e "3) Testing"
        read -p "Select environment [1]: " env_choice
        env_choice=${env_choice:-1}

        case "$env_choice" in
            1) app_env="production" ;;
            2) app_env="local" ;;
            3) app_env="testing" ;;
            *) app_env="production" ;;
        esac

        # Debug mode
        echo -e "${YELLOW}Enable debug mode? (y/n)${NC}"
        read -r debug_mode
        if [[ "$debug_mode" == [Yy]* ]]; then
            app_debug="true"
        else
            app_debug="false"
        fi

        # Update .env file using helper function
        echo -e "${YELLOW}Updating environment settings...${NC}"

        handle_env_file "set" "APP_NAME" "\"$app_name\""
        handle_env_file "set" "APP_URL" "$app_url"
        handle_env_file "set" "APP_ENV" "$app_env"
        handle_env_file "set" "APP_DEBUG" "$app_debug"

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Environment settings updated successfully.${NC}"
        else
            echo -e "${RED}❌ Failed to update environment settings.${NC}"
        fi
    fi

    # Step 2: Container Names Configuration
    echo -e "\n${BOLD}${GREEN}Step 2: Container Names Configuration${NC}"

    echo -e "${YELLOW}Would you like to configure container names? (y/n)${NC}"
    read -r configure_containers
    if [[ "$configure_containers" == [Yy]* ]]; then
        # App container name
        read -p "PHP container name [hcarecloud-app]: " php_container
        php_container=${php_container:-hcarecloud-app}

        # Database container name
        read -p "Database container name [hcarecloud-db]: " db_container
        db_container=${db_container:-hcarecloud-db}

        # Node.js container name
        read -p "Node.js container name [hcarecloud-nodejs]: " nodejs_container
        nodejs_container=${nodejs_container:-hcarecloud-nodejs}

        # Nginx container name
        read -p "Nginx container name [hcarecloud-nginx]: " nginx_container
        nginx_container=${nginx_container:-hcarecloud-nginx}

        # Redis container name
        read -p "Redis container name [hcarecloud-redis]: " redis_container
        redis_container=${redis_container:-hcarecloud-redis}

        # Manager container name
        read -p "Manager container name [hcarecloud-manager]: " manager_container
        manager_container=${manager_container:-hcarecloud-manager}

        # phpMyAdmin container name
        read -p "phpMyAdmin container name [hcarecloud-pma]: " pma_container
        pma_container=${pma_container:-hcarecloud-pma}

        # Update .env file using helper function
        echo -e "${YELLOW}Updating container name settings...${NC}"

        handle_env_file "set" "PHP_CONTAINER_NAME" "$php_container"
        handle_env_file "set" "DB_CONTAINER_NAME" "$db_container"
        handle_env_file "set" "NODEJS_CONTAINER_NAME" "$nodejs_container"
        handle_env_file "set" "NGINX_CONTAINER_NAME" "$nginx_container"
        handle_env_file "set" "REDIS_CONTAINER_NAME" "$redis_container"
        handle_env_file "set" "MANAGER_CONTAINER_NAME" "$manager_container"
        handle_env_file "set" "PHPMYADMIN_CONTAINER_NAME" "$pma_container"

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Container name settings updated successfully.${NC}"
        else
            echo -e "${RED}❌ Failed to update container name settings.${NC}"
        fi
    fi

    # Step 3: Database Configuration
    echo -e "\n${BOLD}${GREEN}Step 3: Database Configuration${NC}"

    echo -e "${YELLOW}Would you like to configure database settings? (y/n)${NC}"
    read -r configure_db
    if [[ "$configure_db" == [Yy]* ]]; then
        # Database host
        read -p "Database host [db]: " db_host
        db_host=${db_host:-db}

        # Database port
        read -p "Database port [3306]: " db_port
        db_port=${db_port:-3306}

        # Database name
        read -p "Database name [hcarecloud]: " db_name
        db_name=${db_name:-hcarecloud}

        # Database username
        read -p "Database username [root]: " db_user
        db_user=${db_user:-root}

        # Database password
        read -sp "Database password [root]: " db_password
        echo ""
        db_password=${db_password:-root}

        # Update .env file using helper function
        echo -e "${YELLOW}Updating database settings...${NC}"

        handle_env_file "set" "DB_HOST" "$db_host"
        handle_env_file "set" "DB_PORT" "$db_port"
        handle_env_file "set" "DB_DATABASE" "$db_name"
        handle_env_file "set" "DB_USERNAME" "$db_user"
        handle_env_file "set" "DB_PASSWORD" "$db_password"

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Database settings updated successfully.${NC}"
        else
            echo -e "${RED}❌ Failed to update database settings.${NC}"
        fi

        # Import demo database
        echo -e "${YELLOW}Would you like to import a demo database? (y/n)${NC}"
        read -r import_demo
        if [[ "$import_demo" == [Yy]* ]]; then
            # List available SQL files
            echo -e "${YELLOW}Available database files:${NC}"

            # Define database directories to scan
            db_dirs=("${PROJECT_ROOT}/database" "${PROJECT_ROOT}/docker/database")

            # Show debug info
            show_debug_info "Scanning for SQL files in: ${db_dirs[*]}"
            show_debug_info "PROJECT_ROOT: ${PROJECT_ROOT}"

            # Find all SQL files locally
            sql_files=()
            for dir in "${db_dirs[@]}"; do
                if [ -d "$dir" ]; then
                    echo -e "${YELLOW}Scanning directory: $dir${NC}"
                    for file in "$dir"/*.sql; do
                        if [ -f "$file" ]; then
                            sql_files+=("$file")
                            echo -e "${GREEN}Found SQL file: $file${NC}"
                        fi
                    done
                else
                    echo -e "${YELLOW}Directory not found: $dir${NC}"
                fi
            done

            # Check for SQL files in Docker container
            if docker-compose exec -T app test -d /var/www/database 2>/dev/null; then
                echo -e "${YELLOW}Scanning Docker container directory: /var/www/database${NC}"
                docker_sql_files=$(docker-compose exec -T app find /var/www/database -name "*.sql" 2>/dev/null)

                if [ -n "$docker_sql_files" ]; then
                    echo -e "${GREEN}Found SQL files in Docker container:${NC}"
                    echo "$docker_sql_files" | while read -r file; do
                        echo -e "${GREEN}Found SQL file in container: $file${NC}"
                        # Add a special prefix to identify Docker container files
                        sql_files+=("DOCKER:$file")
                    done
                fi
            fi

            if [ ${#sql_files[@]} -eq 0 ]; then
                echo -e "${RED}No SQL files found.${NC}"
                echo -e "${YELLOW}Would you like to specify a custom SQL file path? (y/n)${NC}"
                read -r custom_path

                if [[ "$custom_path" == [Yy]* ]]; then
                    read -p "Enter the full path to the SQL file: " custom_sql_path

                    if [ -f "$custom_sql_path" ]; then
                        sql_files+=("$custom_sql_path")
                        echo -e "${GREEN}Found SQL file: $custom_sql_path${NC}"
                    elif docker-compose exec -T app test -f "$custom_sql_path" 2>/dev/null; then
                        sql_files+=("DOCKER:$custom_sql_path")
                        echo -e "${GREEN}Found SQL file in container: $custom_sql_path${NC}"
                    else
                        echo -e "${RED}File not found: $custom_sql_path${NC}"
                    fi
                fi
            fi

            if [ ${#sql_files[@]} -gt 0 ]; then
                # Display available SQL files
                echo -e "${YELLOW}Select a database file to import:${NC}"
                for i in "${!sql_files[@]}"; do
                    file_path="${sql_files[$i]}"

                    # Check if this is a Docker container file
                    if [[ "$file_path" == DOCKER:* ]]; then
                        # Remove the DOCKER: prefix for display
                        docker_path="${file_path#DOCKER:}"
                        file_size=$(docker-compose exec -T app du -h "$docker_path" 2>/dev/null | cut -f1)
                        echo -e "$((i+1))) $(basename "$docker_path") (${file_size:-unknown} - in Docker container)"
                    else
                        echo -e "$((i+1))) $(basename "$file_path") ($(du -h "$file_path" | cut -f1))"
                    fi
                done

                # Select SQL file
                read -p "Select a database file to import [1]: " db_choice
                db_choice=${db_choice:-1}

                if [ $db_choice -ge 1 ] && [ $db_choice -le ${#sql_files[@]} ]; then
                    selected_file="${sql_files[$((db_choice-1))]}"

                    # Check if this is a Docker container file
                    if [[ "$selected_file" == DOCKER:* ]]; then
                        # Remove the DOCKER: prefix
                        docker_path="${selected_file#DOCKER:}"
                        echo -e "${YELLOW}Importing file from Docker container: $docker_path${NC}"

                        # Load debug settings
                        load_env_variables >/dev/null 2>&1

                        # Show more detailed output if in debug mode
                        if [ "$APP_DEBUG" = "true" ]; then
                            echo -e "${YELLOW}Importing database from container (verbose mode)...${NC}"
                            docker-compose exec -T app bash -c "mysql -h db -u\"$db_user\" -p\"$db_password\" \"$db_name\" < $docker_path"

                            if [ $? -eq 0 ]; then
                                echo -e "${GREEN}✅ Database imported successfully.${NC}"
                            else
                                echo -e "${RED}❌ Failed to import database.${NC}"
                            fi
                        else
                            # Use progress bar for import
                            echo -e "${YELLOW}Importing database (this may take a while)...${NC}"

                            # Start import in background
                            docker-compose exec -T app bash -c "mysql -h db -u\"$db_user\" -p\"$db_password\" \"$db_name\" < $docker_path" &
                            import_pid=$!

                            # Show progress bar
                            total_steps=20
                            for ((i=1; i<=total_steps; i++)); do
                                display_progress_bar "$i" "$total_steps" 50 "Importing database"

                                # Check if import is still running
                                if ! kill -0 $import_pid 2>/dev/null; then
                                    # Import has finished
                                    display_progress_bar "$total_steps" "$total_steps" 50 "Importing database"
                                    break
                                fi

                                sleep 1
                            done

                            # Wait for import to finish
                            wait $import_pid
                            import_exit_code=$?

                            if [ $import_exit_code -eq 0 ]; then
                                echo -e "${GREEN}✅ Database imported successfully.${NC}"
                            else
                                echo -e "${RED}❌ Failed to import database.${NC}"
                            fi
                        fi
                    else
                        echo -e "${YELLOW}Importing ${selected_file}...${NC}"

                        # Show file size for debug
                        file_size=$(du -h "$selected_file" | cut -f1)
                        show_debug_info "File size: $file_size"

                        # Load debug settings
                        load_env_variables >/dev/null 2>&1

                        # Show more detailed output if in debug mode
                        if [ "$APP_DEBUG" = "true" ]; then
                            echo -e "${YELLOW}Importing database (verbose mode)...${NC}"
                            docker-compose exec -T db mysql -u"$db_user" -p"$db_password" "$db_name" < "$selected_file"

                            if [ $? -eq 0 ]; then
                                echo -e "${GREEN}✅ Database imported successfully.${NC}"
                            else
                                echo -e "${RED}❌ Failed to import database.${NC}"
                            fi
                        else
                            # Use progress bar for import
                            echo -e "${YELLOW}Importing database (this may take a while)...${NC}"

                            # Start import in background
                            docker-compose exec -T db mysql -u"$db_user" -p"$db_password" "$db_name" < "$selected_file" &
                            import_pid=$!

                            # Show progress bar
                            total_steps=20
                            for ((i=1; i<=total_steps; i++)); do
                                display_progress_bar "$i" "$total_steps" 50 "Importing database"

                                # Check if import is still running
                                if ! kill -0 $import_pid 2>/dev/null; then
                                    # Import has finished
                                    display_progress_bar "$total_steps" "$total_steps" 50 "Importing database"
                                    break
                                fi

                                sleep 1
                            done

                            # Wait for import to finish
                            wait $import_pid
                            import_exit_code=$?

                            if [ $import_exit_code -eq 0 ]; then
                                echo -e "${GREEN}✅ Database imported successfully.${NC}"
                            else
                                echo -e "${RED}❌ Failed to import database.${NC}"
                            fi
                        fi
                    fi
                else
                    echo -e "${RED}Invalid selection.${NC}"
                fi
            fi
        fi
    fi

    # Step 3: Dependencies Installation
    echo -e "\n${BOLD}${GREEN}Step 3: Dependencies Installation${NC}"

    echo -e "${YELLOW}Would you like to install Composer dependencies? (y/n)${NC}"
    read -r install_composer
    if [[ "$install_composer" == [Yy]* ]]; then
        # Check if app container is running
        if ! docker-compose ps | grep -q "app.*Up"; then
            echo -e "${RED}Error: App container is not running. Please start the containers first.${NC}"
            echo -e "${YELLOW}Would you like to start the containers now? (y/n)${NC}"
            read -r start_containers
            if [[ "$start_containers" == [Yy]* ]]; then
                echo -e "${YELLOW}Starting Docker containers...${NC}"
                docker-compose up -d
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Docker containers started successfully.${NC}"
                else
                    echo -e "${RED}❌ Failed to start Docker containers.${NC}"
                    echo -e "${YELLOW}Press Enter to continue...${NC}"
                    read -r
                    return 1
                fi
            else
                echo -e "${YELLOW}Skipping Composer dependencies installation.${NC}"
                return 0
            fi
        fi

        # Get current APP_DEBUG setting from Docker container
        app_debug=$(docker-compose exec -T app bash -c "grep APP_DEBUG /var/www/.env | cut -d= -f2" 2>/dev/null)

        # Show more detailed output if in debug mode
        if [ "$app_debug" = "true" ]; then
            echo -e "${YELLOW}Installing Composer dependencies (verbose mode)...${NC}"
            docker-compose exec app composer install --no-interaction -v

            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✅ Composer dependencies installed successfully.${NC}"
            else
                echo -e "${RED}❌ Failed to install Composer dependencies.${NC}"
                echo -e "${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            fi
        else
            echo -e "${YELLOW}Installing Composer dependencies...${NC}"
            docker-compose exec app composer install --no-interaction

            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✅ Composer dependencies installed successfully.${NC}"
            else
                echo -e "${RED}❌ Failed to install Composer dependencies.${NC}"
                echo -e "${YELLOW}Press Enter to continue...${NC}"
                read -r
                return 1
            fi
        fi
    fi

    echo -e "${YELLOW}Would you like to clear Laravel cache? (y/n)${NC}"
    read -r clear_cache
    if [[ "$clear_cache" == [Yy]* ]]; then
        # Check if app container is running
        if ! docker-compose ps | grep -q "app.*Up"; then
            echo -e "${RED}Error: App container is not running. Cannot clear cache.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi

        echo -e "${YELLOW}Clearing Laravel cache...${NC}"

        # Clear config cache
        echo -e "${YELLOW}Clearing config cache...${NC}"
        docker-compose exec app php artisan config:clear

        # Clear route cache
        echo -e "${YELLOW}Clearing route cache...${NC}"
        docker-compose exec app php artisan route:clear

        # Clear view cache
        echo -e "${YELLOW}Clearing view cache...${NC}"
        docker-compose exec app php artisan view:clear

        # Clear application cache
        echo -e "${YELLOW}Clearing application cache...${NC}"
        docker-compose exec app php artisan cache:clear

        echo -e "${GREEN}✅ All Laravel caches cleared successfully.${NC}"
    fi

    echo -e "${YELLOW}Would you like to create storage link? (y/n)${NC}"
    read -r create_storage_link
    if [[ "$create_storage_link" == [Yy]* ]]; then
        # Check if app container is running
        if ! docker-compose ps | grep -q "app.*Up"; then
            echo -e "${RED}Error: App container is not running. Cannot create storage link.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi

        echo -e "${YELLOW}Creating storage link...${NC}"
        docker-compose exec app php artisan storage:link

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Storage link created successfully.${NC}"
        else
            echo -e "${RED}❌ Failed to create storage link.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi
    fi

    # Step 4: Migrations and Seeders
    echo -e "\n${BOLD}${GREEN}Step 4: Migrations and Seeders${NC}"

    echo -e "${YELLOW}Would you like to run database migrations? (y/n)${NC}"
    read -r run_migrations
    if [[ "$run_migrations" == [Yy]* ]]; then
        # Check if app container is running
        if ! docker-compose ps | grep -q "app.*Up"; then
            echo -e "${RED}Error: App container is not running. Cannot run migrations.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi

        # Check if db container is running
        if ! docker-compose ps | grep -q "db.*Up"; then
            echo -e "${RED}Error: Database container is not running. Cannot run migrations.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi

        echo -e "${YELLOW}Preserve existing tables? (y/n)${NC}"
        read -r preserve_tables

        if [[ "$preserve_tables" == [Yy]* ]]; then
            # Show warning about preserving tables
            echo -e "${YELLOW}⚠️ Running migrations while preserving existing tables.${NC}"
            echo -e "${YELLOW}This will add any missing tables without affecting your existing data.${NC}"

            # Get current APP_DEBUG setting from Docker container
            app_debug=$(docker-compose exec -T app bash -c "grep APP_DEBUG /var/www/.env | cut -d= -f2" 2>/dev/null)

            # Show more detailed output if in debug mode
            if [ "$app_debug" = "true" ]; then
                echo -e "${YELLOW}Running migrations with --force (verbose mode)...${NC}"
                docker-compose exec app php artisan migrate --force -v

                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Migrations completed successfully.${NC}"
                else
                    echo -e "${RED}❌ Failed to run migrations.${NC}"
                    echo -e "${YELLOW}Press Enter to continue...${NC}"
                    read -r
                    return 1
                fi
            else
                echo -e "${YELLOW}Running migrations with --force...${NC}"
                docker-compose exec app php artisan migrate --force

                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Migrations completed successfully.${NC}"
                else
                    echo -e "${RED}❌ Failed to run migrations.${NC}"
                    echo -e "${YELLOW}Press Enter to continue...${NC}"
                    read -r
                    return 1
                fi
            fi
        else
            # Show warning about fresh migrations
            echo -e "${RED}⚠️ WARNING: Running fresh migrations will delete all existing data!${NC}"
            echo -e "${YELLOW}Are you sure you want to continue? This cannot be undone. (y/n)${NC}"
            read -r confirm_fresh

            if [[ "$confirm_fresh" == [Yy]* ]]; then
                # Get current APP_DEBUG setting from Docker container
                app_debug=$(docker-compose exec -T app bash -c "grep APP_DEBUG /var/www/.env | cut -d= -f2" 2>/dev/null)

                # Show more detailed output if in debug mode
                if [ "$app_debug" = "true" ]; then
                    echo -e "${YELLOW}Running fresh migrations with --force (verbose mode)...${NC}"
                    docker-compose exec app php artisan migrate:fresh --force -v

                    if [ $? -eq 0 ]; then
                        echo -e "${GREEN}✅ Fresh migrations completed successfully.${NC}"
                    else
                        echo -e "${RED}❌ Failed to run fresh migrations.${NC}"
                        echo -e "${YELLOW}Press Enter to continue...${NC}"
                        read -r
                        return 1
                    fi
                else
                    echo -e "${YELLOW}Running fresh migrations with --force...${NC}"
                    docker-compose exec app php artisan migrate:fresh --force

                    if [ $? -eq 0 ]; then
                        echo -e "${GREEN}✅ Fresh migrations completed successfully.${NC}"
                    else
                        echo -e "${RED}❌ Failed to run fresh migrations.${NC}"
                        echo -e "${YELLOW}Press Enter to continue...${NC}"
                        read -r
                        return 1
                    fi
                fi
            else
                echo -e "${YELLOW}Fresh migrations cancelled.${NC}"
            fi
        fi
    fi

    echo -e "${YELLOW}Would you like to run database seeders? (y/n)${NC}"
    read -r run_seeders
    if [[ "$run_seeders" == [Yy]* ]]; then
        # Check if app container is running
        if ! docker-compose ps | grep -q "app.*Up"; then
            echo -e "${RED}Error: App container is not running. Cannot run seeders.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi

        # Check if db container is running
        if ! docker-compose ps | grep -q "db.*Up"; then
            echo -e "${RED}Error: Database container is not running. Cannot run seeders.${NC}"
            echo -e "${YELLOW}Press Enter to continue...${NC}"
            read -r
            return 1
        fi

        # Show warning about seeders
        echo -e "${YELLOW}⚠️ WARNING: Running seeders may cause duplicate data if you've already imported a database.${NC}"
        echo -e "${YELLOW}Are you sure you want to continue? (y/n)${NC}"
        read -r confirm_seeders

        if [[ "$confirm_seeders" == [Yy]* ]]; then
            # Get current APP_DEBUG setting from Docker container
            app_debug=$(docker-compose exec -T app bash -c "grep APP_DEBUG /var/www/.env | cut -d= -f2" 2>/dev/null)

            # Show more detailed output if in debug mode
            if [ "$app_debug" = "true" ]; then
                echo -e "${YELLOW}Running database seeders (verbose mode)...${NC}"
                docker-compose exec app php artisan db:seed --force -v

                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Seeders completed successfully.${NC}"
                else
                    echo -e "${RED}❌ Failed to run seeders.${NC}"
                    echo -e "${YELLOW}Press Enter to continue...${NC}"
                    read -r
                    return 1
                fi
            else
                echo -e "${YELLOW}Running database seeders...${NC}"
                docker-compose exec app php artisan db:seed --force

                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Seeders completed successfully.${NC}"
                else
                    echo -e "${RED}❌ Failed to run seeders.${NC}"
                    echo -e "${YELLOW}Press Enter to continue...${NC}"
                    read -r
                    return 1
                fi
            fi
        else
            echo -e "${YELLOW}Database seeders cancelled.${NC}"
        fi
    fi

    # Setup completed
    echo -e "\n${BOLD}${GREEN}Setup Completed!${NC}"
    echo -e "${YELLOW}Your H-CareCloud application has been set up successfully.${NC}"

    # Show application URL using helper function
    app_url=$(handle_env_file "get" "APP_URL")
    if [ -z "$app_url" ]; then
        app_url="${APP_URL:-http://localhost:8000}"
    fi

    echo -e "${YELLOW}You can now access your application at: ${app_url}${NC}"

    # Ask if user wants to restart the app container
    echo -e "${YELLOW}Would you like to restart the app container to apply changes? (y/n)${NC}"
    read -r restart_app
    if [[ "$restart_app" == [Yy]* ]]; then
        echo -e "${YELLOW}Restarting app container...${NC}"
        docker-compose restart app
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ App container restarted successfully.${NC}"
        else
            echo -e "${RED}❌ Failed to restart app container.${NC}"
        fi
    fi

    # Final message
    echo -e "\n${BOLD}${GREEN}Setup Wizard Complete!${NC}"
    echo -e "${YELLOW}Press Enter to return to the main menu...${NC}"
    read -r
}

# Function to check system health
check_system_health() {
    echo -e "${BOLD}${BLUE}🏥 System Health Check${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    local issues=0

    # Check Docker service
    echo -e "${YELLOW}Checking Docker service...${NC}"
    if docker info &>/dev/null; then
        echo -e "${GREEN}✅ Docker service is running.${NC}"
    else
        echo -e "${RED}❌ Docker service is not running.${NC}"
        ((issues++))
    fi

    # Check container health
    echo -e "\n${YELLOW}Checking container health...${NC}"
    local unhealthy_containers=$(docker ps --filter "health=unhealthy" --format "{{.Names}}")
    if [ -z "$unhealthy_containers" ]; then
        echo -e "${GREEN}✅ All containers are healthy.${NC}"
    else
        echo -e "${RED}❌ Unhealthy containers found:${NC}"
        echo "$unhealthy_containers"
        ((issues++))
    fi

    # Check disk space
    echo -e "\n${YELLOW}Checking disk space...${NC}"
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        echo -e "${GREEN}✅ Disk space is adequate (${disk_usage}% used).${NC}"
    else
        echo -e "${RED}❌ Low disk space warning (${disk_usage}% used).${NC}"
        ((issues++))
    fi

    # Check required ports
    echo -e "\n${YELLOW}Checking required ports...${NC}"
    local required_ports=(80 6600 3306 6379)
    for port in "${required_ports[@]}"; do
        if ! lsof -i ":$port" &>/dev/null; then
            echo -e "${GREEN}✅ Port $port is available.${NC}"
        else
            echo -e "${RED}❌ Port $port is in use.${NC}"
            ((issues++))
        fi
    done

    # Summary
    echo -e "\n${YELLOW}Health Check Summary:${NC}"
    if [ $issues -eq 0 ]; then
        echo -e "${GREEN}✅ All systems are healthy.${NC}"
    else
        echo -e "${RED}❌ Found $issues issue(s) that need attention.${NC}"
    fi

    return $issues
}

# Function to view resource usage
view_resource_usage() {
    echo -e "${BOLD}${BLUE}📈 Resource Usage${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    if ! ensure_docker_running; then
        echo -e "${RED}Cannot view resource usage because Docker is not running.${NC}"
        return 1
    fi

    echo -e "${YELLOW}Container Resource Usage:${NC}"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"

    echo -e "\n${YELLOW}Disk Space Usage:${NC}"
    echo -e "${CYAN}Docker Volumes:${NC}"
    docker system df -v

    echo -e "\n${CYAN}Project Directory:${NC}"
    du -sh "${PROJECT_ROOT}"/*

    echo -e "\n${YELLOW}System Resources:${NC}"
    echo -e "${CYAN}CPU Usage:${NC}"
    top -bn1 | head -n 5

    echo -e "\n${CYAN}Memory Usage:${NC}"
    free -h

    echo -e "\n${CYAN}Disk Usage:${NC}"
    df -h | grep -v tmpfs

    return 0
}

# Function to toggle screen clear
toggle_screen_clear() {
    echo -e "${BOLD}${BLUE}🔄 Toggle Screen Clear${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    if [ "$CLEAR_SCREEN" = true ]; then
        CLEAR_SCREEN=false
        echo -e "${GREEN}✅ Screen clearing disabled.${NC}"
    else
        CLEAR_SCREEN=true
        echo -e "${GREEN}✅ Screen clearing enabled.${NC}"
    fi
}

# Function to open web UI
open_web_ui() {
    echo -e "${BOLD}${BLUE}🌐 Open Web UI${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Choose interface to open:${NC}"
    echo -e "  ${GREEN}1.${NC} Main application"
    echo -e "  ${GREEN}2.${NC} Manager interface"
    echo -e "  ${GREEN}3.${NC} phpMyAdmin"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-3]: "
    read -r choice

    case $choice in
        1)
            local app_url=$(grep "^APP_URL=" "${PROJECT_ROOT}/.env" | cut -d= -f2)
            if [ -z "$app_url" ]; then
                app_url="${APP_URL:-http://localhost}"
            fi
            echo -e "${YELLOW}Opening $app_url in your browser...${NC}"
            if command -v xdg-open &>/dev/null; then
                xdg-open "$app_url"
            elif command -v open &>/dev/null; then
                open "$app_url"
            elif command -v start &>/dev/null; then
                start "$app_url"
            else
                echo -e "${RED}❌ Could not open browser automatically.${NC}"
                echo -e "${YELLOW}Please open $app_url manually.${NC}"
            fi
            ;;
        2)
            local manager_url="${MANAGER_URL:-http://localhost:6600}"
            echo -e "${YELLOW}Opening $manager_url in your browser...${NC}"
            if command -v xdg-open &>/dev/null; then
                xdg-open "$manager_url"
            elif command -v open &>/dev/null; then
                open "$manager_url"
            elif command -v start &>/dev/null; then
                start "$manager_url"
            else
                echo -e "${RED}❌ Could not open browser automatically.${NC}"
                echo -e "${YELLOW}Please open $manager_url manually.${NC}"
            fi
            ;;
        3)
            local phpmyadmin_url="${PHPMYADMIN_URL:-http://localhost:8080}"
            echo -e "${YELLOW}Opening $phpmyadmin_url in your browser...${NC}"
            if command -v xdg-open &>/dev/null; then
                xdg-open "$phpmyadmin_url"
            elif command -v open &>/dev/null; then
                open "$phpmyadmin_url"
            elif command -v start &>/dev/null; then
                start "$phpmyadmin_url"
            else
                echo -e "${RED}❌ Could not open browser automatically.${NC}"
                echo -e "${YELLOW}Please open $phpmyadmin_url manually.${NC}"
            fi
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            return 1
            ;;
    esac
}



# Function to show Docker monitor
show_docker_monitor() {
    if [ -f "${SCRIPT_DIR}/ru-docker-monitor.sh" ]; then
        "${SCRIPT_DIR}/ru-docker-monitor.sh"
    else
        echo -e "${RED}Error: ru-docker-monitor.sh not found at ${SCRIPT_DIR}/ru-docker-monitor.sh${NC}"
        echo -e "${YELLOW}Checking alternative locations...${NC}"

        if [ -f "${SCRIPT_DIR}/bash/ru-docker-monitor.sh" ]; then
            "${SCRIPT_DIR}/bash/ru-docker-monitor.sh"
        else
            echo -e "${RED}Error: ru-docker-monitor.sh not found in any standard location${NC}"
            echo -e "${YELLOW}Using built-in monitoring instead...${NC}"

            # Display a more informative header
            echo -e "\n${BOLD}${BLUE}═══════════ DOCKER CONTAINER STATISTICS ═══════════${NC}\n"
            docker stats --no-stream

            # Also show system resources
            echo -e "\n${BOLD}${BLUE}═══════════ SYSTEM RESOURCES ═══════════${NC}\n"
            echo -e "${CYAN}CPU Usage:${NC}"
            top -bn1 | head -n 5

            echo -e "\n${CYAN}Memory Usage:${NC}"
            free -h

            echo -e "\n${CYAN}Disk Usage:${NC}"
            df -h | grep -v tmpfs

            return 1
        fi
    fi
}

# Function to show Docker resources
show_docker_resources() {
    echo -e "${BLUE}${BOLD}Docker Volumes${NC}"
    echo -e "${CYAN}───────────────────────────────────────────────────────────${NC}"
    docker volume ls --filter name=hcarecloud

    echo -e "\n${BLUE}${BOLD}Docker Networks${NC}"
    echo -e "${CYAN}───────────────────────────────────────────────────────────${NC}"
    docker network ls --filter name=hcarecloud

    echo -e "\n${BLUE}${BOLD}Docker Images${NC}"
    echo -e "${CYAN}───────────────────────────────────────────────────────────${NC}"
    docker images | grep -E 'hcarecloud|hostwek'
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-system.sh"
    return 1
fi