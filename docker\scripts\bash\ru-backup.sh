#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║                    H-CareCloud Professional Backup System                ║
# ║                                                                          ║
# ║ Professional backup system for H-CareCloud with version tracking,        ║
# ║ environment awareness, and comprehensive data protection.                 ║
# ║                                                                          ║
# ║ Copyright (c) 2025 Hostwek LTD. All rights reserved.                     ║
# ║ Author: <PERSON> <<EMAIL>>                             ║
# ║ Version: 1.0.5                                                           ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Source helper functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
else
    echo -e "${RED}FATAL ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}" >&2
    return 1
fi

if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "${RED}FATAL ERROR: PROJECT_ROOT not set. ru-helper.sh must be sourced properly.${NC}" >&2
    return 1
fi

# Configuration
BACKUP_DIR="$PROJECT_ROOT/docker/backups"
TEMP_DIR="$BACKUP_DIR/temp"
LOGS_DIR="$BACKUP_DIR/logs"

# Ensure backup directories exist
ensure_backup_directories() {
    local app_version=$(get_app_version)
    
    mkdir -p "$BACKUP_DIR"/{logs,temp}
    mkdir -p "$BACKUP_DIR/snapshots"/{v$app_version,archive}
    mkdir -p "$BACKUP_DIR/database"/{v$app_version,daily}
    mkdir -p "$BACKUP_DIR/volumes"/{persistent,uploads,cache}
    mkdir -p "$BACKUP_DIR/configurations"/{environment,docker,application}
    mkdir -p "$BACKUP_DIR/metadata"
    
    log_info "Backup directories ensured for version $app_version"
}

# Get app version from .env
get_app_version() {
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        grep "^APP_VERSION=" "$PROJECT_ROOT/.env" | cut -d'=' -f2 | tr -d '"' | tr -d "'"
    else
        echo "1.0.5"
    fi
}

# Generate timestamp
get_timestamp() {
    date +"%Y%m%d_%H%M%S"
}

# Log backup operation
log_backup_operation() {
    local operation="$1"
    local status="$2"
    local details="$3"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] $operation - $status: $details" >> "$LOGS_DIR/backup_operations.log"
}

# Professional backup menu
show_backup_menu() {
    clear
    print_header "H-CareCloud Professional Backup System"
    
    local app_version=$(get_app_version)
    echo -e "${BLUE}Current Version: ${BOLD}v$app_version${NC}"
    echo -e "${BLUE}Backup Directory: ${BOLD}$BACKUP_DIR${NC}"
    echo ""
    
    echo -e "${BOLD}${MAGENTA}📦 BACKUP OPTIONS${NC}"
    echo -e "   ${GREEN}1.${NC} 📸 Complete System Snapshot"
    echo -e "   ${GREEN}2.${NC} 🗄️ Database Only Backup"
    echo -e "   ${GREEN}3.${NC} 💾 Docker Volumes Backup"
    echo -e "   ${GREEN}4.${NC} ⚙️ Configuration Backup"
    echo ""
    
    echo -e "${BOLD}${CYAN}📋 MANAGEMENT OPTIONS${NC}"
    echo -e "   ${GREEN}5.${NC} 📂 List Available Backups"
    echo -e "   ${GREEN}6.${NC} 🔍 View Backup Details"
    echo -e "   ${GREEN}7.${NC} 🗑️ Delete Old Backups"
    echo -e "   ${GREEN}8.${NC} 📊 Backup System Status"
    echo ""
    
    echo -e "${BOLD}${YELLOW}🔄 RESTORE OPTIONS${NC}"
    echo -e "   ${GREEN}9.${NC} 🔄 Restore Database"
    echo -e "   ${GREEN}10.${NC} 🔄 Restore Volumes"
    echo -e "   ${GREEN}11.${NC} 🔄 Full System Restore"
    echo ""
    
    echo -e "${BOLD}${RED}0.${NC} ← Back to Main Menu"
    echo ""
}

# Create complete system snapshot
create_complete_snapshot() {
    local description="$1"
    local app_version=$(get_app_version)
    local timestamp=$(get_timestamp)
    local snapshot_name="hcarecloud_snapshot_v${app_version}_${timestamp}"
    
    if [[ -n "$description" ]]; then
        local desc_clean=$(echo "$description" | tr ' ' '_' | tr -cd '[:alnum:]_-')
        snapshot_name="${snapshot_name}_${desc_clean}"
    fi
    
    local snapshot_dir="$TEMP_DIR/$snapshot_name"
    local snapshot_file="$BACKUP_DIR/snapshots/v$app_version/${snapshot_name}.tar.gz"
    
    log_info "Creating complete system snapshot: $snapshot_name"
    log_backup_operation "SNAPSHOT_START" "INFO" "Starting snapshot creation: $snapshot_name"
    
    # Create temporary snapshot directory
    mkdir -p "$snapshot_dir"
    
    # 1. Backup database
    log_info "Step 1/4: Backing up database..."
    if backup_database_to_dir "$snapshot_dir/database.sql"; then
        log_info "✓ Database backup completed"
    else
        log_error "✗ Database backup failed"
        rm -rf "$snapshot_dir"
        return 1
    fi
    
    # 2. Backup project files (excluding large directories)
    log_info "Step 2/4: Backing up project files..."
    if backup_project_files "$snapshot_dir/project_files.tar.gz"; then
        log_info "✓ Project files backup completed"
    else
        log_error "✗ Project files backup failed"
        rm -rf "$snapshot_dir"
        return 1
    fi
    
    # 3. Backup configurations
    log_info "Step 3/4: Backing up configurations..."
    mkdir -p "$snapshot_dir/configurations"
    cp "$PROJECT_ROOT/.env" "$snapshot_dir/configurations/" 2>/dev/null || true
    cp "$PROJECT_ROOT/docker-compose.yml" "$snapshot_dir/configurations/" 2>/dev/null || true
    cp "$PROJECT_ROOT/docker-compose.override.yml" "$snapshot_dir/configurations/" 2>/dev/null || true
    log_info "✓ Configuration backup completed"
    
    # 4. Create metadata
    log_info "Step 4/4: Creating metadata..."
    cat > "$snapshot_dir/snapshot_metadata.json" << EOF
{
    "snapshot_name": "$snapshot_name",
    "app_version": "$app_version",
    "created_at": "$(date -Iseconds)",
    "description": "$description",
    "type": "complete_snapshot",
    "components": {
        "database": true,
        "project_files": true,
        "configurations": true,
        "volumes": false
    },
    "system_info": {
        "hostname": "$(hostname)",
        "user": "$(whoami)",
        "docker_version": "$(docker --version 2>/dev/null || echo 'N/A')"
    }
}
EOF
    
    # 5. Create final archive
    log_info "Creating final snapshot archive..."
    cd "$TEMP_DIR"
    tar -czf "$snapshot_file" "$snapshot_name"
    
    # Clean up temporary directory
    rm -rf "$snapshot_dir"
    
    if [[ -f "$snapshot_file" ]]; then
        local file_size=$(du -h "$snapshot_file" | cut -f1)
        log_info "✓ Complete snapshot created successfully: $snapshot_file ($file_size)"
        log_backup_operation "SNAPSHOT_COMPLETE" "SUCCESS" "Snapshot created: $snapshot_file ($file_size)"
        return 0
    else
        log_error "✗ Failed to create snapshot archive"
        log_backup_operation "SNAPSHOT_COMPLETE" "FAILED" "Failed to create snapshot archive"
        return 1
    fi
}

# Backup database to specific file
backup_database_to_dir() {
    local output_file="$1"
    
    if ! docker ps | grep -q "${DB_CONTAINER_NAME}"; then
        log_error "Database container ${DB_CONTAINER_NAME} is not running"
        return 1
    fi
    
    # Validate environment variables for security
    if [ -z "${DB_DATABASE}" ] || [ -z "${DB_USERNAME}" ] || [ -z "${DB_PASSWORD}" ] || [ -z "${DB_CONTAINER_NAME}" ]; then
        log_error "Required database environment variables not set"
        return 1
    fi
    
    # Create database backup
    docker exec ${DB_CONTAINER_NAME} mysqldump \
        -u"${DB_USERNAME}" \
        -p"${DB_PASSWORD}" \
        --single-transaction \
        --routines \
        --triggers \
        --quick \
        --lock-tables=false \
        --add-drop-table \
        --add-drop-database \
        --create-options \
        --disable-keys \
        --extended-insert \
        "${DB_DATABASE}" > "$output_file" 2>/dev/null
    
    if [[ $? -eq 0 && -s "$output_file" ]]; then
        return 0
    else
        return 1
    fi
}

# Backup project files
backup_project_files() {
    local output_file="$1"
    
    cd "$PROJECT_ROOT"
    
    # Create tar archive excluding large directories
    tar -czf "$output_file" \
        --exclude='node_modules' \
        --exclude='vendor' \
        --exclude='.git' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='dist' \
        --exclude='build' \
        --exclude='docker/backups' \
        --exclude='storage/logs' \
        --exclude='storage/framework/cache' \
        --exclude='storage/framework/sessions' \
        --exclude='storage/framework/views' \
        --exclude='.DS_Store' \
        --exclude='Thumbs.db' \
        . 2>/dev/null
    
    if [[ $? -eq 0 && -f "$output_file" ]]; then
        return 0
    else
        return 1
    fi
}

# Quick project backup (simplified version)
quick_project_backup() {
    local app_version=$(get_app_version)
    local timestamp=$(get_timestamp)
    local backup_name="hcarecloud_quick_v${app_version}_${timestamp}"
    local backup_file="$BACKUP_DIR/snapshots/v$app_version/${backup_name}.tar.gz"
    
    log_info "Creating quick project backup..."
    log_backup_operation "QUICK_BACKUP_START" "INFO" "Starting quick backup: $backup_name"
    
    if backup_project_files "$backup_file"; then
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_info "✓ Quick backup completed: $backup_file ($file_size)"
        log_backup_operation "QUICK_BACKUP_COMPLETE" "SUCCESS" "Quick backup created: $backup_file ($file_size)"
        return 0
    else
        log_error "✗ Quick backup failed"
        log_backup_operation "QUICK_BACKUP_COMPLETE" "FAILED" "Quick backup failed"
        return 1
    fi
}

# List available backups
list_backups() {
    local app_version=$(get_app_version)
    
    echo -e "${BOLD}${BLUE}📂 Available Backups for v$app_version${NC}"
    echo ""
    
    # Snapshots
    echo -e "${BOLD}Complete Snapshots:${NC}"
    if ls "$BACKUP_DIR/snapshots/v$app_version"/*.tar.gz >/dev/null 2>&1; then
        for file in "$BACKUP_DIR/snapshots/v$app_version"/*.tar.gz; do
            local filename=$(basename "$file")
            local size=$(du -h "$file" | cut -f1)
            local date=$(stat -c %y "$file" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1)
            echo "  📸 $filename ($size) - $date"
        done
    else
        echo "  No snapshots found"
    fi
    echo ""
    
    # Database backups
    echo -e "${BOLD}Database Backups:${NC}"
    if ls "$BACKUP_DIR/database/v$app_version"/*.sql >/dev/null 2>&1; then
        for file in "$BACKUP_DIR/database/v$app_version"/*.sql; do
            local filename=$(basename "$file")
            local size=$(du -h "$file" | cut -f1)
            local date=$(stat -c %y "$file" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1)
            echo "  🗄️ $filename ($size) - $date"
        done
    else
        echo "  No database backups found"
    fi
    echo ""
}

# Handler function for backup management (22-23)
handle_backup_management() {
    local choice=$1

    case $choice in
        22)
            # Professional Backup System
            run_backup_system
            ;;
        23)
            # Restore Backup
            echo -e "${YELLOW}Restore functionality - showing available backups...${NC}"
            list_backups
            echo -e "\n${YELLOW}Press Enter to continue...${NC}"
            read -r
            ;;
        *)
            echo -e "${RED}Invalid backup option.${NC}"
            return 1
            ;;
    esac

    return 0
}

# Main backup function
run_backup_system() {
    ensure_backup_directories

    while true; do
        show_backup_menu
        read -p "Select an option: " choice
        
        case $choice in
            1)
                echo ""
                read -p "Enter backup description: " description
                if [[ -n "$description" ]]; then
                    create_complete_snapshot "$description"
                else
                    log_error "Description is required for professional backups"
                fi
                read -p "Press Enter to continue..."
                ;;
            2)
                echo ""
                read -p "Enter backup description: " description
                # Database backup would be implemented here
                log_info "Database backup feature - use web interface for full functionality"
                read -p "Press Enter to continue..."
                ;;
            5)
                list_backups
                read -p "Press Enter to continue..."
                ;;
            8)
                echo -e "${BOLD}${BLUE}📊 Backup System Status${NC}"
                echo "Backup Directory: $BACKUP_DIR"
                echo "Available Space: $(df -h "$BACKUP_DIR" | tail -1 | awk '{print $4}')"
                echo "App Version: v$(get_app_version)"
                echo ""
                read -p "Press Enter to continue..."
                ;;
            0)
                return 0
                ;;
            *)
                log_error "Invalid option. Please try again."
                sleep 1
                ;;
        esac
    done
}

# Export functions for use in runall.sh
export -f run_backup_system
export -f quick_project_backup
