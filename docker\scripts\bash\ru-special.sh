#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║           H-CareCloud Special Operations                                 ║
# ║                                                                          ║
# ║ This script provides special operations for the H-CareCloud              ║
# ║ management system. It is used by runall.sh to handle special operations. ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Ensure SCRIPT_DIR is defined
if [ -z "${SCRIPT_DIR}" ]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
fi

if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
    echo -e "${CYAN}PROJECT_ROOT: ${PROJECT_ROOT}${NC}"
else
    echo -e "${RED}FATAL ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}" >&2
    return 1
fi

if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "${RED}FATAL ERROR: PROJECT_ROOT not set. ru-helper.sh must be sourced properly.${NC}" >&2
    return 1
fi

# Source common functions if not already sourced
if ! type execute_with_progress &>/dev/null; then
    if [ -f "${SCRIPT_DIR}/ru-functions.sh" ]; then
        source "${SCRIPT_DIR}/ru-functions.sh"
    else
        echo -e "${RED}FATAL ERROR: ru-functions.sh not found at ${SCRIPT_DIR}/ru-functions.sh${NC}" >&2
        return 1
    fi
fi

# Function to fix line endings
fix_line_endings() {
    echo -e "${BOLD}${BLUE}🔧 Fixing Line Endings${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Find all text files in the Docker directory and convert CRLF to LF
    find ./docker -type f -name "*.sh" -o -name "Dockerfile" -o -name "*.py" -o -name "*.txt" -o -name "*.conf" | while read -r file; do
        echo -e "Fixing line endings in ${file}"
        # Check if file has CRLF line endings
        if grep -q $'\r' "$file"; then
            # Create a temporary file
            tr -d '\r' < "$file" > "${file}.tmp"
            # Replace the original file with the fixed one
            mv "${file}.tmp" "$file"
            # Make sure shell scripts are executable
            if [[ "$file" == *.sh ]]; then
                chmod +x "$file"
            fi
            echo -e "${GREEN}Fixed line endings in ${file}${NC}"
        else
            echo -e "${GREEN}File already has correct line endings: ${file}${NC}"
        fi
    done

    # Specifically fix the requirements.txt file
    if [ -f "./docker/scripts/hcm/requirements.txt" ]; then
        echo -e "Fixing line endings in requirements.txt"
        tr -d '\r' < "./docker/scripts/hcm/requirements.txt" > "./docker/scripts/hcm/requirements.txt.tmp"
        mv "./docker/scripts/hcm/requirements.txt.tmp" "./docker/scripts/hcm/requirements.txt"
        echo -e "${GREEN}Fixed line endings in requirements.txt${NC}"
    fi
}

# Function to fix permissions
fix_permissions() {
    echo -e "${BOLD}${BLUE}🔧 Fixing Permissions${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Fixing permissions for Docker scripts...${NC}"
    find ./docker/scripts -name "*.sh" -exec chmod +x {} \;
    echo -e "${GREEN}Script permissions fixed.${NC}"

    echo -e "${YELLOW}Fixing permissions for storage directory...${NC}"
    if [ -d "./storage" ]; then
        chmod -R 775 ./storage
        echo -e "${GREEN}Storage permissions fixed.${NC}"
    else
        echo -e "${RED}Storage directory not found.${NC}"
    fi

    echo -e "${YELLOW}Fixing permissions for bootstrap/cache directory...${NC}"
    if [ -d "./bootstrap/cache" ]; then
        chmod -R 775 ./bootstrap/cache
        echo -e "${GREEN}Bootstrap/cache permissions fixed.${NC}"
    else
        echo -e "${RED}Bootstrap/cache directory not found.${NC}"
    fi
}

# Function to fix dependencies
fix_dependencies() {
    echo -e "${BOLD}${BLUE}🔧 Fixing Dependencies${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    echo -e "${YELLOW}Choose dependency to fix:${NC}"
    echo -e "  ${GREEN}1.${NC} Fix Node.js dependencies"
    echo -e "  ${GREEN}2.${NC} Fix Composer dependencies"
    echo -e "  ${GREEN}3.${NC} Fix all dependencies"
    echo -e "  ${GREEN}0.${NC} Cancel"

    echo -ne "${CYAN}Enter your choice${NC} [0-3]: "
    read -r choice

    case $choice in
        1)
            echo -e "${YELLOW}Fixing Node.js dependencies...${NC}"
            # Use centralized function if available
            if type docker_nodejs_exec &>/dev/null; then
                docker_nodejs_exec "cd /var/www && npm cache clean --force" ""
                docker_nodejs_exec "cd /var/www && rm -rf node_modules" ""
                docker_nodejs_exec "cd /var/www && npm install" ""
            else
                # Fallback to direct Docker commands
                docker-compose exec nodejs sh -c "cd /var/www && npm cache clean --force"
                docker-compose exec nodejs sh -c "cd /var/www && rm -rf node_modules"
                docker-compose exec nodejs sh -c "cd /var/www && npm install"
            fi
            echo -e "${GREEN}✅ Node.js dependencies fixed.${NC}"
            ;;
        2)
            echo -e "${YELLOW}Fixing Composer dependencies...${NC}"
            docker-compose exec app composer clear-cache
            docker-compose exec app rm -rf vendor
            docker-compose exec app composer install
            echo -e "${GREEN}✅ Composer dependencies fixed.${NC}"
            ;;
        3)
            # Fix Node.js dependencies
            echo -e "${YELLOW}Fixing Node.js dependencies...${NC}"
            # Use centralized function if available
            if type docker_nodejs_exec &>/dev/null; then
                docker_nodejs_exec "cd /var/www && npm cache clean --force" ""
                docker_nodejs_exec "cd /var/www && rm -rf node_modules" ""
                docker_nodejs_exec "cd /var/www && npm install" ""
            else
                # Fallback to direct Docker commands
                docker-compose exec nodejs sh -c "cd /var/www && npm cache clean --force"
                docker-compose exec nodejs sh -c "cd /var/www && rm -rf node_modules"
                docker-compose exec nodejs sh -c "cd /var/www && npm install"
            fi

            # Fix Composer dependencies
            echo -e "${YELLOW}Fixing Composer dependencies...${NC}"
            docker-compose exec app composer clear-cache
            docker-compose exec app rm -rf vendor
            docker-compose exec app composer install

            # Rebuild containers
            echo -e "${YELLOW}Rebuilding containers...${NC}"
            docker-compose build --no-cache manager

            # Ask if user wants to start the containers
            echo -e "${YELLOW}Do you want to start the containers now? (y/n)${NC}"
            read -r start_containers

            if [[ "$start_containers" == "y" || "$start_containers" == "Y" ]]; then
                echo -e "${YELLOW}Starting containers...${NC}"
                docker-compose up -d
            else
                echo -e "${YELLOW}Containers not started. You can start them later with:${NC} docker-compose up -d"
            fi

            echo -e "${GREEN}✅ All dependencies fixed.${NC}"
            ;;
        0)
            echo -e "${YELLOW}Operation cancelled.${NC}"
            ;;
        *)
            echo -e "${RED}Invalid option.${NC}"
            ;;
    esac
}

# Function to fix npm vulnerabilities
fix_npm_vulnerabilities() {
    echo -e "${BOLD}${BLUE}🛡️ Fixing NPM Vulnerabilities${NC}"
    echo -e "${CYAN}────────────────────────────────────────${NC}"

    # Check if Docker is running
    if ! ensure_docker_running; then
        echo -e "${RED}Docker is not running. Cannot fix NPM vulnerabilities.${NC}"
        return 1
    fi

    # Ask user which project to fix
    echo -e "${YELLOW}Which project would you like to fix vulnerabilities for?${NC}"
    echo -e "${GREEN}1.${NC} Main Application"
    echo -e "${GREEN}2.${NC} Web Manager UI"
    echo -e "${GREEN}3.${NC} All Projects"
    echo -e "${GREEN}0.${NC} Cancel"

    local project_choice
    read -p "Enter your choice [0-3]: " project_choice

    case $project_choice in
        1)
            # Fix main application vulnerabilities
            echo -e "${CYAN}Fixing vulnerabilities in main application...${NC}"
            docker-compose exec app bash -c "cd /var/www && npm audit fix --force"
            ;;
        2)
            # Fix web manager UI vulnerabilities
            echo -e "${CYAN}Fixing vulnerabilities in web manager UI...${NC}"
            docker-compose exec nodejs sh -c "cd ${MANAGER_THEME_PATH} && npm audit fix --force"
            ;;
        3)
            # Fix all projects
            echo -e "${CYAN}Fixing vulnerabilities in all projects...${NC}"
            echo -e "${YELLOW}Fixing main application...${NC}"
            docker-compose exec app bash -c "cd /var/www && npm audit fix --force"
            echo -e "${YELLOW}Fixing web manager UI...${NC}"
            docker-compose exec nodejs sh -c "cd ${MANAGER_THEME_PATH} && npm audit fix --force"
            ;;
        0)
            echo -e "${YELLOW}Operation cancelled.${NC}"
            return 0
            ;;
        *)
            echo -e "${RED}Invalid choice.${NC}"
            return 1
            ;;
    esac

    echo -e "${GREEN}NPM vulnerabilities fixed successfully.${NC}"
    echo -e "${YELLOW}Note: You may need to rebuild the affected projects.${NC}"

    # Ask if user wants to rebuild
    local rebuild_choice
    read -p "Would you like to rebuild the projects now? (y/n): " rebuild_choice

    if [[ "$rebuild_choice" =~ ^[Yy]$ ]]; then
        case $project_choice in
            1)
                # Rebuild main application
                docker-compose exec app bash -c "cd /var/www && npm run build"
                ;;
            2)
                # Rebuild web manager UI
                docker-compose exec nodejs sh -c "cd ${MANAGER_THEME_PATH} && npx vite build"
                ;;
            3)
                # Rebuild all projects
                docker-compose exec app bash -c "cd /var/www && npm run build"
                docker-compose exec nodejs sh -c "cd ${MANAGER_THEME_PATH} && npx vite build"
                ;;
        esac
        echo -e "${GREEN}Rebuild completed successfully.${NC}"
    fi

    return 0
}

# Function to handle special operations menu
handle_special_operations() {
    local choice=$1

    case $choice in
        29)
            # Fix Line Ending Issues
            echo -e "${YELLOW}Fixing line ending issues in project files...${NC}"
            echo -e "${CYAN}This will convert CRLF line endings to LF in bash scripts and configuration files.${NC}"

            # Find and fix line endings in bash scripts
            find "${PROJECT_ROOT}/docker/scripts/bash" -name "*.sh" -type f -exec dos2unix {} \; 2>/dev/null || {
                echo -e "${YELLOW}dos2unix not found, using sed to fix line endings...${NC}"
                find "${PROJECT_ROOT}/docker/scripts/bash" -name "*.sh" -type f -exec sed -i 's/\r$//' {} \;
            }

            # Fix line endings in configuration files
            find "${PROJECT_ROOT}" -name "*.env*" -type f -exec sed -i 's/\r$//' {} \; 2>/dev/null
            find "${PROJECT_ROOT}" -name "docker-compose*.yml" -type f -exec sed -i 's/\r$//' {} \; 2>/dev/null

            echo -e "${GREEN}Line ending issues fixed successfully.${NC}"
            ;;
        30)
            # Fix Docker Desktop Issues
            echo -e "${YELLOW}Fixing common Docker Desktop issues...${NC}"

            echo -e "${CYAN}Checking Docker Desktop status...${NC}"
            if ! docker info >/dev/null 2>&1; then
                echo -e "${RED}Docker is not running. Please start Docker Desktop.${NC}"
                return 1
            fi

            echo -e "${CYAN}1. Restarting Docker Desktop service...${NC}"
            if command -v net.exe >/dev/null 2>&1; then
                # Windows
                net.exe stop com.docker.service 2>/dev/null || true
                sleep 2
                net.exe start com.docker.service 2>/dev/null || true
            fi

            echo -e "${CYAN}2. Pruning Docker system...${NC}"
            docker system prune -f

            echo -e "${CYAN}3. Checking Docker version...${NC}"
            docker --version
            docker-compose --version

            echo -e "${GREEN}Docker Desktop issues fixed.${NC}"
            ;;
        31)
            # Code Quality & Cleanup Operations
            echo -e "${BOLD}${BLUE}🔍 Code Quality & Cleanup Operations${NC}"
            echo -e "${CYAN}────────────────────────────────────────${NC}"

            # Source and run the cleanup utility
            if [ -f "${SCRIPT_DIR}/ru-cleanup.sh" ]; then
                source "${SCRIPT_DIR}/ru-cleanup.sh"
                run_from_menu
            else
                echo -e "${RED}Error: ru-cleanup.sh not found${NC}"
                return 1
            fi
            ;;
        *)
            echo -e "${RED}Invalid special operation option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

                    ;;
                3)
                    echo -e "${CYAN}Available networks:${NC}"
                    docker network ls --format "table {{.ID}}\t{{.Name}}\t{{.Driver}}"
                    echo -ne "${CYAN}Enter network ID or name (or 'all' to remove all unused networks):${NC} "
                    read -r net
                    if [ -n "$net" ]; then
                        if [ "$net" = "all" ]; then
                            docker network prune -f
                            echo -e "${GREEN}All unused networks removed.${NC}"
                        else
                            docker network rm "$net" 2>/dev/null && echo -e "${GREEN}Network $net removed.${NC}" || echo -e "${RED}Failed to remove network $net.${NC}"
                        fi
                    else
                        echo -e "${RED}No network specified.${NC}"
                    fi
                    ;;
                4)
                    echo -e "${CYAN}Available images:${NC}"
                    docker image ls --format "table {{.ID}}\t{{.Repository}}:{{.Tag}}\t{{.Size}}"
                    echo -ne "${CYAN}Enter image ID or name (or 'all' to remove all unused images):${NC} "
                    read -r img
                    if [ -n "$img" ]; then
                        if [ "$img" = "all" ]; then
                            docker image prune -af
                            echo -e "${GREEN}All unused images removed.${NC}"
                        else
                            docker rmi "$img" 2>/dev/null && echo -e "${GREEN}Image $img removed.${NC}" || echo -e "${RED}Failed to remove image $img.${NC}"
                        fi
                    else
                        echo -e "${RED}No image specified.${NC}"
                    fi
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        33)
            # Fix Line Ending Issues
            echo -e "${BOLD}${BLUE}🧹 Clean Up Docker Setup${NC}"
            echo -e "${CYAN}────────────────────────────────────────${NC}"

            echo -e "${YELLOW}Available cleanup options:${NC}"
            echo -e "  ${GREEN}1.${NC} Remove unused containers"
            echo -e "  ${GREEN}2.${NC} Remove unused volumes"
            echo -e "  ${GREEN}3.${NC} Remove unused networks"
            echo -e "  ${GREEN}4.${NC} Remove unused images"
            echo -e "  ${GREEN}5.${NC} Remove all build caches"
            echo -e "  ${GREEN}6.${NC} Full system prune (all of the above)"
            echo -e "  ${GREEN}0.${NC} Return to main menu"

            echo -ne "\n${CYAN}Enter your choice${NC} [0-6]: "
            read -r cleanup_choice

            case $cleanup_choice in
                0)
                    echo -e "${YELLOW}Returning to main menu...${NC}"
                    ;;
                1)
                    echo -e "${YELLOW}This will remove all stopped containers.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_containers

                    if [[ "$confirm_containers" == [Yy]* ]]; then
                        execute_with_progress "Removing unused containers" "docker container prune -f"
                        echo -e "${GREEN}Unused containers removed.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                2)
                    echo -e "${YELLOW}This will remove all unused volumes.${NC}"
                    echo -e "${RED}Warning: This will permanently delete data in unused volumes!${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_volumes

                    if [[ "$confirm_volumes" == [Yy]* ]]; then
                        execute_with_progress "Removing unused volumes" "docker volume prune -f"
                        echo -e "${GREEN}Unused volumes removed.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                3)
                    echo -e "${YELLOW}This will remove all unused networks.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_networks

                    if [[ "$confirm_networks" == [Yy]* ]]; then
                        execute_with_progress "Removing unused networks" "docker network prune -f"
                        echo -e "${GREEN}Unused networks removed.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                4)
                    echo -e "${YELLOW}This will remove all unused images.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_images

                    if [[ "$confirm_images" == [Yy]* ]]; then
                        execute_with_progress "Removing unused images" "docker image prune -f"
                        echo -e "${GREEN}Unused images removed.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                5)
                    echo -e "${YELLOW}This will remove all build caches.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_cache

                    if [[ "$confirm_cache" == [Yy]* ]]; then
                        execute_with_progress "Removing build cache" "docker builder prune -f"
                        echo -e "${GREEN}Build cache removed.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                6)
                    echo -e "${YELLOW}This will perform a full system prune (all unused containers, networks, images, and volumes).${NC}"
                    echo -e "${RED}Warning: This is a destructive operation that will permanently delete data!${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_all

                    if [[ "$confirm_all" == [Yy]* ]]; then
                        echo -e "${RED}Final confirmation: This will delete ALL unused Docker resources. Type 'confirm' to proceed:${NC} "
                        read -r final_confirm

                        if [[ "$final_confirm" == "confirm" ]]; then
                            execute_with_progress "Performing full system prune" "docker system prune -af --volumes"
                            echo -e "${GREEN}Docker system fully cleaned.${NC}"
                        else
                            echo -e "${YELLOW}Full system prune cancelled.${NC}"
                        fi
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        34)
            # Fix Docker Desktop Issues
            echo -e "${BOLD}${BLUE}🛠️ Fix Database Issues${NC}"
            echo -e "${CYAN}────────────────────────────────────────${NC}"

            echo -e "${YELLOW}Available options:${NC}"
            echo -e "  ${GREEN}1.${NC} Restart database container"
            echo -e "  ${GREEN}2.${NC} Check database connection"
            echo -e "  ${GREEN}3.${NC} Fix user permissions"
            echo -e "  ${GREEN}4.${NC} Clear database cache"
            echo -e "  ${GREEN}0.${NC} Return to main menu"

            echo -ne "\n${CYAN}Enter your choice${NC} [0-4]: "
            read -r db_fix_choice

            case $db_fix_choice in
                0)
                    echo -e "${YELLOW}Returning to main menu...${NC}"
                    ;;
                1)
                    echo -e "${YELLOW}This will restart the database container.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_restart

                    if [[ "$confirm_restart" == [Yy]* ]]; then
                        execute_with_progress "Restarting database container" "docker-compose restart db"
                        echo -e "${GREEN}Database container restarted.${NC}"
                    else
                        echo -e "${YELLOW}Database restart cancelled.${NC}"
                    fi
                    ;;
                2)
                    echo -e "${YELLOW}Checking database connection...${NC}"
                    if docker-compose exec db mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "SELECT 1;" &>/dev/null; then
                        echo -e "${GREEN}✓ Database connection successful (as root).${NC}"
                    else
                        echo -e "${RED}✗ Cannot connect to database as root.${NC}"
                    fi

                    if docker-compose exec db mysql -u "${MYSQL_USER}" -p"${MYSQL_PASSWORD}" -e "SELECT 1;" &>/dev/null; then
                        echo -e "${GREEN}✓ Database connection successful (as ${MYSQL_USER}).${NC}"
                    else
                        echo -e "${RED}✗ Cannot connect to database as ${MYSQL_USER}.${NC}"
                        echo -e "${YELLOW}Would you like to fix user permissions? (y/n):${NC} "
                        read -r fix_permissions

                        if [[ "$fix_permissions" == [Yy]* ]]; then
                            execute_with_progress "Fixing user permissions" "docker-compose exec db mysql -u root -p\"${MYSQL_ROOT_PASSWORD}\" -e \"GRANT ALL PRIVILEGES ON *.* TO '${MYSQL_USER}'@'%'; FLUSH PRIVILEGES;\""
                            echo -e "${GREEN}User permissions fixed.${NC}"
                        fi
                    fi
                    ;;
                3)
                    echo -e "${YELLOW}This will reset all database user permissions.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_fix

                    if [[ "$confirm_fix" == [Yy]* ]]; then
                        execute_with_progress "Fixing user permissions" "docker-compose exec db mysql -u root -p\"${MYSQL_ROOT_PASSWORD}\" -e \"GRANT ALL PRIVILEGES ON *.* TO '${MYSQL_USER}'@'%'; FLUSH PRIVILEGES;\""
                        echo -e "${GREEN}User permissions fixed.${NC}"
                    else
                        echo -e "${YELLOW}Permission fix cancelled.${NC}"
                    fi
                    ;;
                4)
                    echo -e "${YELLOW}This will clear the database cache.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_clear

                    if [[ "$confirm_clear" == [Yy]* ]]; then
                        execute_with_progress "Clearing database cache" "docker-compose exec db mysql -u root -p\"${MYSQL_ROOT_PASSWORD}\" -e \"RESET QUERY CACHE; FLUSH TABLES;\""
                        echo -e "${GREEN}Database cache cleared.${NC}"
                    else
                        echo -e "${YELLOW}Cache clear cancelled.${NC}"
                    fi
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        35)
            # Clean Docker System
            echo -e "${BOLD}${BLUE}🐳 Fix Docker Desktop Issues${NC}"
            echo -e "${CYAN}────────────────────────────────────────${NC}"

            echo -e "${YELLOW}Available options:${NC}"
            echo -e "  ${GREEN}1.${NC} Gracefully stop all containers"
            echo -e "  ${GREEN}2.${NC} Restart Docker services"
            echo -e "  ${GREEN}3.${NC} Fix Docker networking issues"
            echo -e "  ${GREEN}4.${NC} Reset Docker Desktop (requires manual restart)"
            echo -e "  ${GREEN}0.${NC} Return to main menu"

            echo -ne "\n${CYAN}Enter your choice${NC} [0-4]: "
            read -r docker_fix_choice

            case $docker_fix_choice in
                0)
                    echo -e "${YELLOW}Returning to main menu...${NC}"
                    ;;
                1)
                    echo -e "${YELLOW}This will gracefully stop all running Docker containers.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_stop

                    if [[ "$confirm_stop" == [Yy]* ]]; then
                        execute_with_progress "Stopping all containers" "docker-compose down"
                        echo -e "${GREEN}All containers stopped gracefully.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                2)
                    echo -e "${YELLOW}This will restart all Docker services.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_restart

                    if [[ "$confirm_restart" == [Yy]* ]]; then
                        execute_with_progress "Stopping all containers" "docker-compose down"
                        execute_with_progress "Starting all containers" "docker-compose up -d"
                        echo -e "${GREEN}All Docker services restarted.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                3)
                    echo -e "${YELLOW}This will fix Docker networking issues by pruning networks.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_network

                    if [[ "$confirm_network" == [Yy]* ]]; then
                        execute_with_progress "Pruning Docker networks" "docker network prune -f"
                        echo -e "${GREEN}Docker networks reset successfully.${NC}"
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                4)
                    echo -e "${RED}Warning: This will shut down all containers and require a manual restart of Docker Desktop.${NC}"
                    echo -ne "${YELLOW}Do you want to continue? (y/n):${NC} "
                    read -r confirm_reset

                    if [[ "$confirm_reset" == [Yy]* ]]; then
                        execute_with_progress "Stopping all containers" "docker-compose down"
                        echo -e "${YELLOW}Please restart Docker Desktop manually now.${NC}"
                        echo -e "${YELLOW}Press Enter after Docker Desktop has been restarted...${NC}"
                        read -r

                        echo -ne "${YELLOW}Would you like to start all containers again? (y/n):${NC} "
                        read -r start_again
                        if [[ "$start_again" == [Yy]* ]]; then
                            execute_with_progress "Starting all containers" "docker-compose up -d"
                            echo -e "${GREEN}All containers started.${NC}"
                        fi
                    else
                        echo -e "${YELLOW}Operation cancelled.${NC}"
                    fi
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        36)
            # Code Cleanup Utility
            echo -e "${BOLD}${BLUE}🔍 Code Cleanup Utility${NC}"
            echo -e "${CYAN}────────────────────────────────────────${NC}"

            echo -e "${YELLOW}Available cleanup options:${NC}"
            echo -e "  ${GREEN}1.${NC} Fix line endings (CRLF to LF)"
            echo -e "  ${GREEN}2.${NC} Clean Node.js files"
            echo -e "  ${GREEN}3.${NC} Clean Composer cache"
            echo -e "  ${GREEN}4.${NC} Fix file permissions"
            echo -e "  ${GREEN}0.${NC} Return to main menu"

            echo -ne "\n${CYAN}Enter your choice${NC} [0-4]: "
            read -r cleanup_choice

            case $cleanup_choice in
                0)
                    echo -e "${YELLOW}Operation cancelled.${NC}"
                    ;;
                1)
                    fix_line_endings
                    ;;
                2)
                    clean_nodejs_files
                    ;;
                3)
                    echo -e "${YELLOW}Cleaning Composer cache...${NC}"
                    docker-compose exec app composer clear-cache
                    echo -e "${GREEN}✅ Composer cache cleaned.${NC}"
                    ;;
                4)
                    echo -e "${YELLOW}Fixing file permissions...${NC}"
                    find ./docker -name "*.sh" -exec chmod +x {} \;
                    echo -e "${GREEN}✅ File permissions fixed.${NC}"
                    ;;
                *)
                    echo -e "${RED}Invalid option.${NC}"
                    ;;
            esac
            ;;
        *)
            echo -e "${RED}Invalid special operation option.${NC}"
            return 1
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-special.sh"
    exit 1
fi
                # Make sure the script is executable
                chmod +x "${SCRIPT_DIR}/ru-clean-nodejs.sh"
                # Execute the ru-clean-nodejs.sh script directly
                "${SCRIPT_DIR}/ru-clean-nodejs.sh"
            else
                echo -e "${RED}Error: ru-clean-nodejs.sh script not found.${NC}"
                echo -e "${YELLOW}Checking alternative locations...${NC}"

                # Check alternative locations
                ALTERNATIVE_PATHS=(
                    "${PROJECT_ROOT}/docker/scripts/bash/ru-clean-nodejs.sh"
                    "${PROJECT_ROOT}/docker/scripts/ru-clean-nodejs.sh"
                    "${PROJECT_ROOT}/scripts/ru-clean-nodejs.sh"
                    "${PROJECT_ROOT}/ru-clean-nodejs.sh"
                )

                FOUND=false
                for path in "${ALTERNATIVE_PATHS[@]}"; do
                    if [ -f "$path" ]; then
                        echo -e "${GREEN}Found ru-clean-nodejs.sh at $path${NC}"
                        chmod +x "$path"
                        "$path"
                        FOUND=true
                        break
                    fi
                done

                if [ "$FOUND" = false ]; then
                    echo -e "${RED}ru-clean-nodejs.sh script not found in any standard location.${NC}"
                    echo -e "${YELLOW}Attempting to clean Node.js files directly...${NC}"

                    # Implement basic Node.js cleaning functionality
                    echo -e "${YELLOW}Choose what to clean:${NC}"
                    echo -e "  ${GREEN}1.${NC} Clean node_modules"
                    echo -e "  ${GREEN}2.${NC} Clean npm cache"
                    echo -e "  ${GREEN}3.${NC} Clean build artifacts"
                    echo -e "  ${GREEN}4.${NC} Clean everything"
                    echo -e "  ${GREEN}5.${NC} Clean Python cache (__pycache__ directories)"
                    echo -e "  ${GREEN}0.${NC} Cancel"

                    echo -ne "${CYAN}Enter your choice${NC} [0-5]: "
                    read -r nodejs_clean_choice

                    case $nodejs_clean_choice in
                        0)
                            echo -e "${YELLOW}Operation cancelled.${NC}"
                            ;;
                        1)
                            echo -e "${YELLOW}Cleaning node_modules directory...${NC}"
                            if docker info > /dev/null 2>&1 && docker-compose ps | grep -q nodejs; then
                                # Use centralized function if available
                                if type docker_nodejs_exec &>/dev/null; then
                                    execute_with_progress "Removing node_modules" \
                                        "docker_nodejs_exec 'cd /var/www && rm -rf node_modules' ''"
                                else
                                    # Fallback to direct Docker commands
                                    execute_with_progress "Removing node_modules" \
                                        "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf node_modules'"
                                fi
                                echo -e "${GREEN}node_modules directory cleaned.${NC}"
                            else
                                echo -e "${YELLOW}NodeJS container not running. Attempting to remove directly...${NC}"
                                if [ -d "${PROJECT_ROOT}/node_modules" ]; then
                                    execute_with_progress "Removing node_modules" \
                                        "rm -rf ${PROJECT_ROOT}/node_modules"
                                    echo -e "${GREEN}node_modules directory cleaned.${NC}"
                                else
                                    echo -e "${RED}No node_modules directory found.${NC}"
                                fi
                            fi
                            ;;
                        2)
                            echo -e "${YELLOW}Cleaning npm cache...${NC}"
                            # Try local npm first
                            if command -v npm &> /dev/null; then
                                execute_with_progress "Cleaning npm cache" \
                                    "npm cache clean --force"
                                echo -e "${GREEN}npm cache cleaned.${NC}"
                            # Try Docker container if available
                            elif command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1 && command -v docker-compose >/dev/null 2>&1; then
                                if docker-compose ps 2>/dev/null | grep -q nodejs; then
                                    execute_with_progress "Cleaning npm cache via Docker" \
                                        "docker-compose exec -T nodejs npm cache clean --force"
                                    echo -e "${GREEN}npm cache cleaned via Docker container.${NC}"
                                else
                                    echo -e "${YELLOW}Node.js container not running. Trying with temporary container...${NC}"
                                    execute_with_progress "Cleaning npm cache via temporary container" \
                                        "docker run --rm -v \"${PROJECT_ROOT}:/app\" node:14-alpine sh -c 'cd /app && npm cache clean --force'"
                                    echo -e "${GREEN}npm cache cleaned via temporary container.${NC}"
                                fi
                            else
                                echo -e "${RED}npm not found and Docker not available. Cannot clean npm cache.${NC}"
                            fi
                            ;;
                        3)
                            echo -e "${YELLOW}Cleaning build artifacts...${NC}"
                            if docker info > /dev/null 2>&1 && docker-compose ps | grep -q nodejs; then
                                execute_with_progress "Removing dist folder" \
                                    "docker-compose exec nodejs rm -rf /var/www/public/dist"
                                execute_with_progress "Removing manager theme dist folder" \
                                    "docker-compose exec nodejs rm -rf /var/www/${MANAGER_THEME_PATH}/dist"
                                echo -e "${GREEN}Build artifacts cleaned.${NC}"
                            else
                                echo -e "${YELLOW}NodeJS container not running. Trying to clean build artifacts directly...${NC}"
                                if [ -d "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/dist" ]; then
                                    execute_with_progress "Removing manager theme dist folder" \
                                        "rm -rf ${PROJECT_ROOT}/${MANAGER_THEME_PATH}/dist"
                                    echo -e "${GREEN}Manager theme build cleaned.${NC}"
                                else
                                    echo -e "${RED}No manager theme dist folder found.${NC}"
                                fi
                            fi
                            ;;
                        4)
                            echo -e "${YELLOW}Cleaning everything (node_modules, npm cache, and build artifacts)...${NC}"
                            if docker info > /dev/null 2>&1 && docker-compose ps | grep -q nodejs; then
                                # Use centralized function if available
                                if type docker_nodejs_exec &>/dev/null; then
                                    execute_with_progress "Removing node_modules" \
                                        "docker_nodejs_exec 'cd /var/www && rm -rf node_modules' ''"
                                    execute_with_progress "Clearing npm cache" \
                                        "docker_nodejs_exec 'cd /var/www && npm cache clean --force' ''"
                                else
                                    # Fallback to direct Docker commands
                                    execute_with_progress "Removing node_modules" \
                                        "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf node_modules'"
                                    execute_with_progress "Clearing npm cache" \
                                        "docker-compose exec nodejs sh -c 'cd /var/www && npm cache clean --force'"
                                fi
                                # Use centralized function if available
                                if type docker_nodejs_exec &>/dev/null; then
                                    execute_with_progress "Removing dist folder" \
                                        "docker_nodejs_exec 'cd /var/www && rm -rf public/dist' ''"
                                    execute_with_progress "Removing manager theme dist folder" \
                                        "docker_nodejs_exec 'cd /var/www && rm -rf ${MANAGER_THEME_PATH}/dist' ''"
                                else
                                    # Fallback to direct Docker commands
                                    execute_with_progress "Removing dist folder" \
                                        "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf public/dist'"
                                    execute_with_progress "Removing manager theme dist folder" \
                                        "docker-compose exec nodejs sh -c 'cd /var/www && rm -rf ${MANAGER_THEME_PATH}/dist'"
                                fi
                                echo -e "${GREEN}All Node.js files cleaned.${NC}"
                            else
                                echo -e "${YELLOW}NodeJS container not running. Attempting to clean files directly...${NC}"
                                if [ -d "${PROJECT_ROOT}/node_modules" ]; then
                                    execute_with_progress "Removing node_modules" \
                                        "rm -rf ${PROJECT_ROOT}/node_modules"
                                fi
                                if command -v npm &> /dev/null; then
                                    execute_with_progress "Cleaning local npm cache" \
                                        "npm cache clean --force"
                                fi
                                if [ -d "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/dist" ]; then
                                    execute_with_progress "Removing manager theme dist folder" \
                                        "rm -rf ${PROJECT_ROOT}/${MANAGER_THEME_PATH}/dist"
                                fi
                                echo -e "${GREEN}All Node.js files cleaned.${NC}"
                            fi
                            ;;
                        5)
                            echo -e "${YELLOW}Cleaning Python cache (__pycache__ directories)...${NC}"
                            local CLEANED=false
                            local SUCCESS_COUNT=0
                            local FAIL_COUNT=0

                            # Try to use Docker container first if available and running
                            if type is_docker_running &>/dev/null && is_docker_running && command -v docker-compose >/dev/null 2>&1; then
                                if docker-compose ps 2>/dev/null | grep -q manager; then
                                    echo -e "${YELLOW}Using manager container to clean Python cache...${NC}"
                                    if docker-compose exec -T manager sh -c "find /var/www -name '__pycache__' -type d -exec rm -rf {} \; 2>/dev/null || true" 2>/dev/null; then
                                        echo -e "${GREEN}Python cache directories cleaned via manager container.${NC}"
                                        CLEANED=true
                                    else
                                        echo -e "${YELLOW}Manager container cleaning failed, trying app container...${NC}"
                                    fi
                                fi

                                if [ "$CLEANED" = false ] && docker-compose ps 2>/dev/null | grep -q app; then
                                    echo -e "${YELLOW}Using app container to clean Python cache...${NC}"
                                    if docker-compose exec -T app sh -c "find /var/www/docker/scripts/hcc_management -name '__pycache__' -type d -exec rm -rf {} \; 2>/dev/null || true" 2>/dev/null; then
                                        echo -e "${GREEN}Python cache directories cleaned via app container.${NC}"
                                        CLEANED=true
                                    else
                                        echo -e "${YELLOW}App container cleaning failed, falling back to local cleaning...${NC}"
                                    fi
                                fi
                            fi

                            # If Docker cleaning failed or wasn't available, try local cleaning
                            if [ "$CLEANED" = false ]; then
                                echo -e "${YELLOW}Attempting to clean Python cache files directly...${NC}"
                                local pycache_dirs=()
                                while IFS= read -r -d '' dir; do
                                    pycache_dirs+=("$dir")
                                done < <(find "${PROJECT_ROOT}/docker/scripts/hcc_management" -name "__pycache__" -type d -print0 2>/dev/null)

                                if [ ${#pycache_dirs[@]} -gt 0 ]; then
                                    echo -e "${YELLOW}Found ${#pycache_dirs[@]} Python cache directories.${NC}"

                                    for dir in "${pycache_dirs[@]}"; do
                                        echo -e "${YELLOW}Removing $dir${NC}"
                                        if rm -rf "$dir" 2>/dev/null; then
                                            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
                                            CLEANED=true
                                        else
                                            FAIL_COUNT=$((FAIL_COUNT + 1))
                                            # Try with sudo if available
                                            if command -v sudo >/dev/null 2>&1; then
                                                echo -e "${YELLOW}Trying with sudo...${NC}"
                                                if sudo rm -rf "$dir" 2>/dev/null; then
                                                    SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
                                                    CLEANED=true
                                                else
                                                    # Try to at least delete the .pyc files if directory removal failed
                                                    echo -e "${YELLOW}Attempting to remove individual .pyc files in $dir${NC}"
                                                    find "$dir" -name "*.pyc" -type f -delete 2>/dev/null
                                                fi
                                            fi
                                        fi
                                    done
                                fi
                            fi

                            if [ "$CLEANED" = true ]; then
                                if [ $FAIL_COUNT -eq 0 ]; then
                                    echo -e "${GREEN}All Python cache directories cleaned successfully.${NC}"
                                else
                                    echo -e "${YELLOW}Cleaned $SUCCESS_COUNT Python cache directories. Failed to clean $FAIL_COUNT directories.${NC}"
                                fi
                            else
                                echo -e "${RED}No Python cache directories found to clean.${NC}"
                            fi
                            ;;
                        *)
                            echo -e "${RED}Invalid option. Please try again.${NC}"
                            ;;
                    esac
                fi
            fi
            ;;
        53)
            # Clean Vendor Folder
            echo -e "${YELLOW}Cleaning vendor folder...${NC}"
            # Source the enhanced ru-clean-vendor.sh script
            if [ -f "${SCRIPT_DIR}/ru-clean-vendor.sh" ]; then
                source "${SCRIPT_DIR}/ru-clean-vendor.sh"
                # Call the clean_vendor_folder function from the script
                clean_vendor_folder
            else
                echo -e "${RED}Error: ru-clean-vendor.sh script not found.${NC}"
                echo -e "${YELLOW}Please ensure the script exists at: ${SCRIPT_DIR}/ru-clean-vendor.sh${NC}"
            fi
            ;;
        54)
            # Fix NPM Vulnerabilities
            fix_npm_vulnerabilities
            ;;
        55)
            # Code Cleanup Utility
            echo -e "${BOLD}${BLUE}🧹 Code Cleanup Utility${NC}"
            echo -e "${CYAN}────────────────────────────────────────${NC}"

            # Source the ru-cleanup.sh script
            if [ -f "${SCRIPT_DIR}/ru-cleanup.sh" ]; then
                # Set flag to indicate we're running from the menu
                export RUNNING_FROM_MENU=true

                # Source the script and call the run_from_menu function
                source "${SCRIPT_DIR}/ru-cleanup.sh"
                run_from_menu

                # Reset the flag
                export RUNNING_FROM_MENU=false
            else
                echo -e "${RED}Error: ru-cleanup.sh script not found.${NC}"
                echo -e "${YELLOW}Please ensure the script exists at: ${SCRIPT_DIR}/ru-cleanup.sh${NC}"
            fi
            ;;
        56)
            # Dev Mode Web Manager UI
            echo -e "${BOLD}${MAGENTA}🛠️ Starting Web Manager UI in Development Mode${NC}"
            echo -e "${CYAN}────────────────────────────────────────────────────────────${NC}"

            # Check if the ru-dev-manager.sh script exists
            if [ -f "${SCRIPT_DIR}/ru-dev-manager.sh" ]; then
                # Make sure the script is executable
                chmod +x "${SCRIPT_DIR}/ru-dev-manager.sh"
                # Execute the ru-dev-manager.sh script directly
                "${SCRIPT_DIR}/ru-dev-manager.sh"
            else
                echo -e "${RED}Error: ru-dev-manager.sh script not found.${NC}"
                echo -e "${YELLOW}Please ensure the script exists at: ${SCRIPT_DIR}/ru-dev-manager.sh${NC}"
            fi
            ;;
        *)
            echo -e "${RED}Invalid special operation option.${NC}"
            return 1
            ;;
    esac

    echo -e "\n${YELLOW}Press Enter to continue...${NC}"
    read -r
    return 0
}

# If this script is run directly, show a message
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "This script is meant to be sourced by runall.sh, not run directly."
    echo "Usage: source ru-special.sh"
    exit 1
fi
