#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║ H-CareCloud Simple Cleanup Utility                                       ║
# ║                                                                          ║
# ║ Simple cleanup for node_modules, vendor, cache, and temporary files      ║
# ║                                                                          ║
# ║ Author: <PERSON> <<EMAIL>>                             ║
# ║ Company: Hostwek LTD – WekTurbo Dev                                       ║
# ║ Version: 1.0.8                                                           ║
# ║ Year: 2025                                                               ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Source helper functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
else
    echo -e "${RED}FATAL ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}" >&2
    return 1
fi

if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "${RED}FATAL ERROR: PROJECT_ROOT not set. ru-helper.sh must be sourced properly.${NC}" >&2
    return 1
fi

# Simple cleanup menu
# Show cleanup menu
show_cleanup_menu() {
    while true; do
        clear
        echo -e "${BOLD}${CYAN}╭─────────────────────────────────────────────────────────────────────────╮${NC}"
        echo -e "${BOLD}${CYAN}│${NC}                    ${BOLD}${GREEN}🧹 SIMPLE CLEANUP UTILITY${NC}                    ${BOLD}${CYAN}│${NC}"
        echo -e "${BOLD}${CYAN}╰─────────────────────────────────────────────────────────────────────────╯${NC}"

        echo -e "\n${BOLD}${MAGENTA}📦 CLEANUP OPTIONS${NC}"
        echo -e "   ${GREEN}1.${NC} 🗂️ Clean node_modules"
        echo -e "   ${GREEN}2.${NC} 📦 Clean vendor folder"
        echo -e "   ${GREEN}3.${NC} 🧹 Clean npm cache"
        echo -e "   ${GREEN}4.${NC} 🗑️ Clean temporary files"
        echo -e "   ${GREEN}5.${NC} 🧼 Clean Laravel cache"
        echo -e "   ${GREEN}6.${NC} 🔄 Clean everything"
        echo -e "   ${GREEN}0.${NC} ⬅️ Back to Main Menu"

        echo -e "\n${BOLD}${CYAN}╭─────────────────────────────────────────────────────────────────────────╮${NC}"
        echo -e "${BOLD}${CYAN}│${NC}                    ${BOLD}${YELLOW}Enter your choice [0-6]:${NC}                     ${BOLD}${CYAN}│${NC}"
        echo -e "${BOLD}${CYAN}╰─────────────────────────────────────────────────────────────────────────╯${NC}"

        echo -ne "${CYAN}Enter your choice${NC} [0-6]: "
        read -r choice

        case $choice in
            0)
                echo -e "${YELLOW}Returning to main menu...${NC}"
                return 0
                ;;
            1)
                clean_node_modules
                ;;
            2)
                clean_vendor
                ;;
            3)
                clean_npm_cache
                ;;
            4)
                clean_temp_files
                ;;
            5)
                clean_laravel_cache
                ;;
            6)
                echo -e "${YELLOW}Cleaning everything...${NC}"
                clean_node_modules
                clean_vendor
                clean_npm_cache
                clean_temp_files
                clean_laravel_cache
                echo -e "${BOLD}${GREEN}✅ All cleanup operations completed!${NC}"
                ;;
            *)
                echo -e "${RED}Invalid option. Please try again.${NC}"
                sleep 1
                ;;
        esac

        if [ "$choice" != "0" ]; then
            echo -e "\n${YELLOW}Press Enter to continue...${NC}"
            read -r
        fi
    done
}

# Clean npm cache
clean_npm_cache() {
    echo -e "${YELLOW}Cleaning npm cache...${NC}"
    if command -v npm >/dev/null 2>&1; then
        npm cache clean --force
        echo -e "${GREEN}✅ npm cache cleaned${NC}"
    else
        echo -e "${YELLOW}npm not found, skipping cache clean${NC}"
    fi
}

# Clean Laravel cache
clean_laravel_cache() {
    echo -e "${YELLOW}Cleaning Laravel cache...${NC}"
    if [ -n "${APP_CONTAINER_NAME}" ]; then
        docker compose exec "${APP_CONTAINER_NAME}" php artisan cache:clear 2>/dev/null || echo -e "${YELLOW}Laravel cache clear failed${NC}"
        docker compose exec "${APP_CONTAINER_NAME}" php artisan config:clear 2>/dev/null || echo -e "${YELLOW}Laravel config clear failed${NC}"
        docker compose exec "${APP_CONTAINER_NAME}" php artisan route:clear 2>/dev/null || echo -e "${YELLOW}Laravel route clear failed${NC}"
        docker compose exec "${APP_CONTAINER_NAME}" php artisan view:clear 2>/dev/null || echo -e "${YELLOW}Laravel view clear failed${NC}"
        echo -e "${GREEN}✅ Laravel cache cleaned${NC}"
    else
        echo -e "${YELLOW}APP_CONTAINER_NAME not set, skipping Laravel cache clean${NC}"
    fi
}

# Clean node_modules
clean_node_modules() {
    echo -e "${YELLOW}Cleaning node_modules...${NC}"
    if [ -d "${PROJECT_ROOT}/node_modules" ]; then
        rm -rf "${PROJECT_ROOT}/node_modules"
        echo -e "${GREEN}✅ node_modules cleaned${NC}"
    fi
    
    # Clean H-CareManager node_modules
    if [ -d "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/node_modules" ]; then
        rm -rf "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/node_modules"
        echo -e "${GREEN}✅ H-CareManager node_modules cleaned${NC}"
    fi
}

# Clean vendor folder
clean_vendor() {
    echo -e "${YELLOW}Cleaning vendor folder...${NC}"
    if [ -d "${PROJECT_ROOT}/vendor" ]; then
        rm -rf "${PROJECT_ROOT}/vendor"
        echo -e "${GREEN}✅ vendor folder cleaned${NC}"
    fi
}

# Clean temporary files
clean_temp_files() {
    echo -e "${YELLOW}Cleaning temporary files...${NC}"
    find "${PROJECT_ROOT}" -name "*.tmp" -type f -delete 2>/dev/null || true
    find "${PROJECT_ROOT}" -name "*.temp" -type f -delete 2>/dev/null || true
    find "${PROJECT_ROOT}" -name "*~" -type f -delete 2>/dev/null || true
    echo -e "${GREEN}✅ Temporary files cleaned${NC}"
}

# Main cleanup function
run_cleanup() {
    show_cleanup_menu
}

# Function called from menu
run_from_menu() {
    show_cleanup_menu
}
