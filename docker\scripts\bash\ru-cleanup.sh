#!/bin/bash
# ╔══════════════════════════════════════════════════════════════════════════╗
# ║ H-CareCloud Simple Cleanup Utility                                       ║
# ║                                                                          ║
# ║ Simple cleanup for node_modules, vendor, cache, and temporary files      ║
# ║                                                                          ║
# ║ Author: <PERSON> <<EMAIL>>                             ║
# ║ Company: Hostwek LTD – WekTurbo Dev                                       ║
# ║ Version: 1.0.8                                                           ║
# ║ Year: 2025                                                               ║
# ╚══════════════════════════════════════════════════════════════════════════╝

# Source helper functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [ -f "${SCRIPT_DIR}/ru-helper.sh" ]; then
    source "${SCRIPT_DIR}/ru-helper.sh"
else
    echo -e "${RED}FATAL ERROR: ru-helper.sh not found at ${SCRIPT_DIR}/ru-helper.sh${NC}" >&2
    return 1
fi

if [ -z "${PROJECT_ROOT}" ]; then
    echo -e "${RED}FATAL ERROR: PROJECT_ROOT not set. ru-helper.sh must be sourced properly.${NC}" >&2
    return 1
fi

# Simple cleanup menu
show_cleanup_menu() {
    clear
    echo -e "${BOLD}${CYAN}╭─────────────────────────────────────────────────────────────────────────╮${NC}"
    echo -e "${BOLD}${CYAN}│${NC}                    ${BOLD}${GREEN}🧹 SIMPLE CLEANUP UTILITY${NC}                    ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}╰─────────────────────────────────────────────────────────────────────────╯${NC}"

    echo -e "\n${BOLD}${MAGENTA}📦 CLEANUP OPTIONS${NC}"
    echo -e "   ${GREEN}1.${NC} 🗂️ Clean node_modules"
    echo -e "   ${GREEN}2.${NC} 📦 Clean vendor folder"
    echo -e "   ${GREEN}3.${NC} 🧹 Clean npm cache"
    echo -e "   ${GREEN}4.${NC} 🗑️ Clean temporary files"
    echo -e "   ${GREEN}5.${NC} 🧼 Clean Laravel cache"
    echo -e "   ${GREEN}6.${NC} 🔄 Clean everything"
    echo -e "   ${GREEN}0.${NC} ⬅️ Back to Main Menu"

    echo -e "\n${BOLD}${CYAN}╭─────────────────────────────────────────────────────────────────────────╮${NC}"
    echo -e "${BOLD}${CYAN}│${NC}                    ${BOLD}${YELLOW}Enter your choice [0-6]:${NC}                     ${BOLD}${CYAN}│${NC}"
    echo -e "${BOLD}${CYAN}╰─────────────────────────────────────────────────────────────────────────╯${NC}"
}

# Clean node_modules
clean_node_modules() {
    echo -e "${YELLOW}Cleaning node_modules...${NC}"
    if [ -d "${PROJECT_ROOT}/node_modules" ]; then
        rm -rf "${PROJECT_ROOT}/node_modules"
        echo -e "${GREEN}✅ node_modules cleaned${NC}"
    fi

    # Clean H-CareManager node_modules
    if [ -d "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/node_modules" ]; then
        rm -rf "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/node_modules"
        echo -e "${GREEN}✅ H-CareManager node_modules cleaned${NC}"
    fi
}

## How to Use These Reports

1. **References to moved files**: Fix these to avoid build errors
2. **Empty directories**: Consider removing these to clean up your codebase
3. **Duplicate lines/code**: Refactor these to improve code quality
4. **Unused imports**: Remove these to reduce bundle size
5. **Syntax errors**: Fix these to prevent runtime errors

## Copyright

© 2025 Hostwek LTD. All rights reserved.
Created by Joseph Matino (<EMAIL>)
Scrum Master: Majok Deng (majokdeng.com)
Company: Hostwek LTD (hostwek.com/wekturbo)
EOL

        echo -e "${GREEN}Created logs directory and README file.${NC}"
    fi

    return 0
}

# Function to display the script header
display_header() {
    clear
    echo -e "\n${BOLD}${BLUE}┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓${NC}"
    echo -e "${BOLD}${BLUE}┃                       🧹 H-CareCloud Cleanup Utility                           ┃${NC}"
    echo -e "${BOLD}${BLUE}┃                                                                              ┃${NC}"
    echo -e "${BOLD}${BLUE}┃${NC}  ${YELLOW}Detect and fix code issues, improve quality, and speed up development${NC}      ${BOLD}${BLUE}┃${NC}"
    echo -e "${BOLD}${BLUE}┃${NC}  ${CYAN}© 2025 Hostwek LTD - Joseph Matino (<EMAIL>)${NC}                  ${BOLD}${BLUE}┃${NC}"
    echo -e "${BOLD}${BLUE}┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛${NC}\n"
}

# Function to get standard exclusion patterns
get_exclusion_patterns() {
    local exclude_dirs="$1"

    # Standard directories to exclude
    local exclusions="node_modules|vendor|\.git|dist|build|cleanup-backup"

    # Add user-specified exclude directories
    if [ -n "$exclude_dirs" ]; then
        exclusions="${exclusions}|${exclude_dirs}"
    fi

    echo "$exclusions"
}

# Centralized function for displaying progress bars
display_progress() {
    local progress=$1      # Percentage (0-100)
    local message=$2       # Optional message to display
    local bar_size=50      # Size of the progress bar

    # Calculate filled portion of the bar
    local bar_filled=$((progress * bar_size / 100))
    local bar=""

    # Create the bar
    for ((i=0; i<bar_filled; i++)); do
        bar="${bar}█"
    done
    for ((i=bar_filled; i<bar_size; i++)); do
        bar="${bar}░"
    done

    # Display the progress bar with message if provided
    if [ -n "$message" ]; then
        printf "${YELLOW}Progress: [${GREEN}%-50s${YELLOW}] %3d%% - %s${NC}\r" "$bar" "$progress" "$message"
    else
        printf "${YELLOW}Progress: [${GREEN}%-50s${YELLOW}] %3d%%${NC}\r" "$bar" "$progress"
    fi
}

# Function to find references to moved files - Optimized version
find_moved_references() {
    local search_dir="$1"
    local moved_dir="$2"
    local output_file="$3"
    local exclude_dirs="$4"

    echo -e "${YELLOW}Searching for references to files moved to $moved_dir...${NC}"

    # Check if moved_dir exists
    if [ ! -d "$moved_dir" ]; then
        echo -e "${RED}Error: Moved directory $moved_dir does not exist.${NC}"
        echo -e "${YELLOW}Please check the path and try again.${NC}"
        return 1
    fi

    # Create or clear the output file
    > "$output_file"

    # Create temporary files
    local temp_dir=$(mktemp -d)
    local basenames_file="$temp_dir/basenames.txt"
    local results_file="$temp_dir/results.txt"
    local search_files="$temp_dir/search_files.txt"
    local patterns_file="$temp_dir/patterns.txt"

    # Set a timeout for the search (5 minutes)
    local timeout=300
    local start_time=$(date +%s)

    echo -e "${CYAN}Step 1/4: Collecting files from moved directory...${NC}"

    # Find all relevant files in the moved directory and extract basenames

# Clean node_modules
clean_node_modules() {
    echo -e "${YELLOW}Cleaning node_modules...${NC}"
    if [ -d "${PROJECT_ROOT}/node_modules" ]; then
        rm -rf "${PROJECT_ROOT}/node_modules"
        echo -e "${GREEN}✅ node_modules cleaned${NC}"
    fi
    
    # Clean H-CareManager node_modules
    if [ -d "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/node_modules" ]; then
        rm -rf "${PROJECT_ROOT}/${MANAGER_THEME_PATH}/node_modules"
        echo -e "${GREEN}✅ H-CareManager node_modules cleaned${NC}"
    fi
}

# Clean vendor folder
clean_vendor() {
    echo -e "${YELLOW}Cleaning vendor folder...${NC}"
    if [ -d "${PROJECT_ROOT}/vendor" ]; then
        rm -rf "${PROJECT_ROOT}/vendor"
        echo -e "${GREEN}✅ vendor folder cleaned${NC}"
    fi
}

# Clean temporary files
clean_temp_files() {
    echo -e "${YELLOW}Cleaning temporary files...${NC}"
    find "${PROJECT_ROOT}" -name "*.tmp" -type f -delete 2>/dev/null || true
    find "${PROJECT_ROOT}" -name "*.temp" -type f -delete 2>/dev/null || true
    find "${PROJECT_ROOT}" -name "*~" -type f -delete 2>/dev/null || true
    echo -e "${GREEN}✅ Temporary files cleaned${NC}"
}

# Main cleanup function
run_cleanup() {
    echo -e "${BOLD}${CYAN}��� Running Cleanup Utility${NC}"
    clean_node_modules
    clean_vendor
    clean_temp_files
    echo -e "${BOLD}${GREEN}✅ Cleanup completed!${NC}"
}

# Function called from menu
run_from_menu() {
    run_cleanup
}
